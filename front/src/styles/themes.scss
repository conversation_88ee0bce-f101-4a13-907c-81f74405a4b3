// Variables de thème pour le mode clair et sombre

// Thème clair (par défaut)
:root {
  // Couleurs primaires
  --primary-color: #4299e1;
  --primary-color-rgb: 66, 153, 225; // Valeur RGB pour les opérations
  --primary-light: #63b3ed;
  --primary-light-rgb: 99, 179, 237;
  --primary-dark: #3182ce;
  --primary-dark-rgb: 49, 130, 206;
  --primary-contrast: #ffffff;
  --primary-contrast-rgb: 255, 255, 255;

  // Couleurs d'accent
  --accent-color: #ed8936;
  --accent-color-rgb: 237, 137, 54; // Valeur RGB pour les opérations
  --accent-light: #f6ad55;
  --accent-light-rgb: 246, 173, 85;
  --accent-dark: #dd6b20;
  --accent-dark-rgb: 221, 107, 32;
  --accent-contrast: #ffffff;
  --accent-contrast-rgb: 255, 255, 255;

  // Couleurs de fond
  --background-color: #ffffff;
  --background-alt: #f7fafc;
  --background-card: #ffffff;
  --background-header: linear-gradient(135deg, #0a192f 0%, #112a5e 100%);

  // Couleurs de texte
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --text-light: #e2e8f0;
  --text-on-primary: #ffffff;
  --text-on-accent: #ffffff;

  // Couleurs de bordure
  --border-color: #e2e8f0;
  --border-light: #edf2f7;
  --border-dark: #cbd5e0;

  // Couleurs d'état
  --success-color: #48bb78;
  --warning-color: #ecc94b;
  --error-color: #f56565;
  --info-color: #4299e1;

  // Ombres
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  // Opacités
  --overlay-opacity: 0.7;
  --disabled-opacity: 0.6;
}

// Thème sombre
.dark-theme {
  // Couleurs primaires
  --primary-color: #4299e1; // Garder la même couleur primaire pour la cohérence
  --primary-color-rgb: 66, 153, 225; // Valeur RGB pour les opérations
  --primary-light: #63b3ed;
  --primary-light-rgb: 99, 179, 237;
  --primary-dark: #2b6cb0;
  --primary-dark-rgb: 43, 108, 176;
  --primary-contrast: #ffffff;
  --primary-contrast-rgb: 255, 255, 255;

  // Couleurs d'accent
  --accent-color: #ed8936; // Garder la même couleur d'accent pour la cohérence
  --accent-color-rgb: 237, 137, 54; // Valeur RGB pour les opérations
  --accent-light: #f6ad55;
  --accent-light-rgb: 246, 173, 85;
  --accent-dark: #dd6b20;
  --accent-dark-rgb: 221, 107, 32;
  --accent-contrast: #ffffff;
  --accent-contrast-rgb: 255, 255, 255;

  // Couleurs de fond
  --background-color: #121212; // Fond plus sombre pour un meilleur contraste
  --background-alt: #1e1e1e; // Fond alternatif légèrement plus clair
  --background-card: #242424; // Cartes légèrement plus claires que le fond
  --background-header: linear-gradient(135deg, #0a192f 0%, #112a5e 100%); // Garder le même dégradé pour les headers

  // Couleurs de texte
  --text-primary: #e2e8f0; // Texte principal légèrement moins blanc pour réduire la fatigue oculaire
  --text-secondary: #a0aec0; // Texte secondaire plus discret
  --text-muted: #718096; // Texte atténué encore plus discret
  --text-light: #4a5568; // Texte clair pour les éléments moins importants
  --text-on-primary: #ffffff; // Texte sur fond primaire
  --text-on-accent: #ffffff; // Texte sur fond d'accent

  // Couleurs de bordure
  --border-color: #333333; // Bordures plus subtiles
  --border-light: #2d2d2d; // Bordures légères
  --border-dark: #1a1a1a; // Bordures foncées

  // Couleurs d'état
  --success-color: #38a169; // Succès
  --warning-color: #d69e2e; // Avertissement
  --error-color: #e53e3e; // Erreur
  --info-color: #3182ce; // Information

  // Ombres (plus subtiles en mode sombre)
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);

  // Opacités
  --overlay-opacity: 0.85; // Overlays plus opaques en mode sombre
  --disabled-opacity: 0.45; // Éléments désactivés légèrement plus visibles
}

// Classes utilitaires pour les transitions
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
