import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { Mat<PERSON><PERSON>erContainer, MatDrawerContent } from "@angular/material/sidenav";
import { TranslateService } from '@ngx-translate/core';
import { ThemeService } from './services/theme/theme.service';
import { ThemeObserverService } from './services/theme/theme-observer.service';
import { ProfileCompletionMessageComponent } from './shared/profile-completion-message/profile-completion-message.component';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    MatDrawerContainer,
    MatDrawerContent,
    ProfileCompletionMessageComponent
  ],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  title = 'front';
  language: string;
  direction = 'ltr';

  constructor(
    private translateService: TranslateService,
    private themeService: ThemeService,
    private themeObserverService: ThemeObserverService
  ) {
    const browserLang = translateService.getBrowserLang();
    this.language = browserLang ? browserLang : 'en';
    this.translateService.setDefaultLang(this.language);
    this.translateService.use(this.language);

    this.setDirection(this.language);
  }

  setDirection(language: string) {
    if (language === 'ar') {
      document.body.setAttribute('dir', 'rtl');
    } else {
      document.body.setAttribute('dir', 'ltr');
    }
  }

  ngOnInit(): void {
    // Initialiser le thème
    this.themeService.initTheme();
  }
}
