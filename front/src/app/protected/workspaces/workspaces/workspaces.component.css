/* ==========================================================================
   Global Styles
   ========================================================================== */
:root {
  --primary-color: #4a90e2; /* Modern blue for accents */
  --secondary-color: #f5f7fa; /* Light gray background */
  --text-color: #ffffff; /* Darker text for better contrast */
  --text-muted: #050080; /* Muted text for secondary info */
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* Softer shadow */
  --card-hover-shadow: 0 6px 16px rgba(0, 0, 0, 0.1); /* Hover shadow */
  --border-radius: 12px; /* Modern rounded corners */
}

/* ==========================================================================
   Workspaces Wrapper
   ========================================================================== */
.workspaces-wrapper {
  max-width: 1500px;
  margin: 0 auto;
  padding: 24px;
  position: relative; /* Nécessaire pour positionner le pseudo-élément */
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 48px);
  font-family: 'Roboto', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.workspaces-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  filter: blur(8px); /* Applique un flou de 8px, ajuste cette valeur selon tes besoins */
  z-index: -1; /* Place l'image floue derrière le contenu */
}
/* ==========================================================================
   Workspaces Header (Title + Search)
   ========================================================================== */
.workspaces-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px; /* Plus d'espace pour une présentation aérée */
  padding: 0 24px; /* Marges latérales pour centrer le contenu */
}

/* ==========================================================================
   Workspaces Title
   ========================================================================== */
.workspaces-title {
  font-size: 28px; /* Taille ajustée pour élégance et lisibilité */
  font-weight: 800; /* Poids accru pour un impact visuel fort */
  color: #1a202c; /* Noir profond et professionnel */
  margin: 0; /* Suppression des marges excessives */
  line-height: 1.2; /* Ratio optimisé pour compacité */
  font-family: 'Inter', sans-serif; /* Police moderne et nette */
  letter-spacing: 0.8px; /* Espacement raffiné */
  text-transform: uppercase; /* Style audacieux maintenu */
  position: relative;
  display: inline-block;
  background: linear-gradient(90deg, #2d3748, #4a5568); /* Dégradé subtil */
  -webkit-background-clip: text; /* Applique le dégradé au texte */
  background-clip: text;
  color: transparent; /* Rend le texte transparent pour voir le dégradé */
  transition: all 0.3s ease; /* Transition fluide pour interactions */
}

/* Soulignement stylisé avec animation */
.workspaces-title::after {
  content: '';
  position: absolute;
  bottom: -6px; /* Rapproché pour un look plus intégré */
  left: 0;
  width: 100%;
  height: 2px; /* Ligne plus fine pour subtilité */
  background: linear-gradient(90deg, #4c51bf, #a3bffa); /* Dégradé indigo moderne */
  transform: scaleX(0.2); /* Ligne partiellement visible au repos */
  transform-origin: left;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* Animation fluide et pro */
}

.workspaces-title:hover::after {
  transform: scaleX(1); /* S'étend complètement au survol */
}

/* ==========================================================================
   Search Bar
   ========================================================================== */
.search-bar {
  width: 420px; /* Légèrement réduit pour une proportion harmonieuse */
  margin-left: 32px; /* Plus d'espace pour séparation */
  background-color: #ffffff;
  border-radius: 12px; /* Coins arrondis modernes */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06); /* Ombre douce et pro */
  transition: all 0.3s ease; /* Transition globale */
}

.search-bar:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1); /* Ombre renforcée au survol */
}

.search-bar .mat-form-field-wrapper {
  padding-bottom: 0;
}

.search-bar .mat-form-field-flex {
  display: flex;
  align-items: center;
  border-radius: 12px; /* Cohérence avec le conteneur */
  padding: 0 16px; /* Espacement interne plus généreux */
  background: #f7fafc; /* Fond gris clair subtil */
  border: 1px solid rgba(203, 213, 224, 0.5); /* Bordure discrète */
  transition: border-color 0.3s ease, background 0.3s ease;
}

.search-bar .mat-form-field-flex:hover {
  border-color: #a3bffa; /* Bordure indigo au survol */
  background: #ffffff; /* Retour au blanc pour contraste */
}

.search-bar .mat-form-field-infix {
  border-top: none;
  padding: 10px 0; /* Espacement vertical ajusté */
}

.search-bar .mat-input-element {
  font-size: 15px; /* Taille légèrement augmentée pour lisibilité */
  font-family: 'Inter', sans-serif; /* Police cohérente */
  color: #2d3748; /* Gris foncé élégant */
  caret-color: #4c51bf; /* Curseur indigo */
}

.search-bar .mat-input-element::placeholder {
  color: #a0aec0; /* Placeholder gris clair et moderne */
  opacity: 1; /* Pleine opacité pour clarté */
}

.search-bar .mat-icon {
  color: #718096; /* Gris bleuté discret */
  font-size: 20px; /* Taille ajustée */
  transition: color 0.3s ease;
}

.search-bar:hover .mat-icon {
  color: #4c51bf; /* Indigo au survol pour cohérence */
}
/* ==========================================================================
   Spinner
   ========================================================================== */
.spinner {
  margin: 40px auto;
  color: var(--primary-color);
}

/* ==========================================================================
   Workspaces Container (Grid Layout)
   ========================================================================== */
.workspaces-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px; /* Increased gap for a more spacious look */
  flex-grow: 1;
}

/* ==========================================================================
   Workspace Card
   ========================================================================== */
.workspace-card {
  padding: 24px; /* Padding équilibré pour plus d'élégance */
  background: #ffffff;
  border-radius: 16px; /* Coins plus arrondis pour un look moderne */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08); /* Ombre douce et professionnelle */
  transition: transform 0.3s ease, box-shadow 0.3s ease, border 0.3s ease;
  border: 1px solid rgba(229, 231, 235, 0.2); /* Bordure subtile et moderne */
  overflow: hidden; /* Sécurité pour les éléments internes */
}

.workspace-card:hover {
  transform: translateY(-4px); /* Élévation plus prononcée */
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12); /* Ombre plus marquée au survol */
  border: 1px solid rgba(79, 70, 229, 0.2); /* Légère teinte indigo */
}

/* ==========================================================================
   Workspace Header
   ========================================================================== */
.workspace-header {
  display: flex;
  align-items: center;
  padding-bottom: 12px; /* Plus d'espace pour respirer */
  position: relative;
}

.workspace-logo {
  width: 28px; /* Légère augmentation pour un look premium */
  height: 28px;
  border-radius: 50%;
  margin-right: 10px;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.9); /* Bordure blanche subtile */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); /* Ombre légère */
}

.workspace-icon {
  font-size: 26px;
  width: 28px;
  height: 28px;
  color: #ffffff;
  background: linear-gradient(135deg, #6b7280, #4b5563); /* Gradient gris sophistiqué */
  border-radius: 50%;
  padding: 6px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.workspace-icon:hover {
  background: linear-gradient(135deg, #5a67d8, #7f9cf5); /* Gradient indigo au survol */
}

.workspace-name {
  font-size: 16px; /* Taille légèrement augmentée */
  font-weight: 700; /* Plus de caractère */
  color: #1a202c; /* Noir profond et professionnel */
  flex-grow: 1;
  letter-spacing: 0.3px; /* Espacement subtil */
}

/* ==========================================================================
   Workspace Content
   ========================================================================== */
.workspace-content {
  padding: 0;
  background: linear-gradient(145deg, #f7fafc, #ffffff); /* Dégradé plus dynamique */
  border-radius: 10px;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.03); /* Ombre interne subtile */
}

.workspace-description {
  font-size: 17px; /* Taille ajustée pour équilibre */
  font-weight: 400;
  color: #2d3748; /* Gris foncé élégant */
  line-height: 1.9; /* Plus d'air pour la lecture */
  margin: 0 0 20px 0;
  text-align: justify;
  padding: 18px; /* Plus d'espace interne */
  letter-spacing: 0.25px;
  font-family: 'Inter', sans-serif; /* Police moderne (à ajouter si disponible) */
}

.workspace-details {
  display: flex;
  flex-direction: column;
  gap: 10px; /* Espacement plus généreux */
  padding: 14px 18px;
}

.workspace-detail {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #6b7280; /* Gris neutre et pro */
  margin: 6px 0;
  transition: color 0.3s ease, transform 0.2s ease;
}

.workspace-detail:hover {
  color: #4a5568; /* Gris plus foncé au survol */
  transform: translateX(4px); /* Déplacement subtil */
}

.detail-icon {
  font-size: 24px; /* Icône plus imposante */
  width: 24px;
  height: 24px;
  margin-right: 12px;
  color: #5a67d8; /* Indigo moderne */
  transition: transform 0.3s ease, color 0.3s ease;
}

.workspace-detail:hover .detail-icon {
  transform: scale(1.15); /* Grossissement élégant */
  color: #434190; /* Indigo plus sombre */
}/* ==========================================================================
   No Workspaces Card
   ========================================================================== */
.no-workspaces-card {
  grid-column: span 2;
  padding: 50px;
  background: rgba(0, 0, 0, 0.45);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  text-align: center;
  border: 1px solid #000000;
}

.no-workspaces-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 8px;
}

.no-workspaces-iconවაპირადაპირველი შეხედვა

.no-workspaces-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: var(--text-muted);
  margin-right: 8px;
}

.no-workspaces-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-muted);
}

/* ==========================================================================
   Paginator
   ========================================================================== */
.workspaces-paginator {
  margin-top: 24px;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
}

.workspaces-paginator .mat-paginator-container {
  justify-content: center;
  padding: 8px 0;
}

.workspaces-paginator .mat-paginator-page-size {
  display: none; /* Masque les options de taille de page si non désiré */
}

.workspaces-paginator .mat-paginator-range-label {
  font-size: 14px;
  color: var(--text-muted); /* Utilise votre couleur muted #050080 */
}

/* Boutons de navigation (précédent/suivant) */
.workspaces-paginator .mat-paginator-navigation-previous,
.workspaces-paginator .mat-paginator-navigation-next {
  background: whitesmoke; /* Fond gris clair #f5f7fa */
  border: 1px solid var(--primary-color); /* Bordure bleue #4a90e2 */
  border-radius: 8px;
  width: 1000px;
  height: 100px;
  margin: 0 4px;
  transition: background 0.3s ease, border-color 0.3s ease;
}

.workspaces-paginator .mat-paginator-navigation-previous:hover,
.workspaces-paginator .mat-paginator-navigation-next:hover {
  background: var(--primary-color); /* Fond bleu #4a90e2 au survol */
  border-color: var(--primary-color); /* Bordure bleue assortie */
}

/* Boutons des pages */
.workspaces-paginator .mat-paginator-page {
  background: var(--secondary-color); /* Fond gris clair #f5f7fa */
  border: 1px solid var(--text-muted); /* Bordure avec #050080 */
  border-radius: 8px;
  width: 36px;
  height: 36px;
  margin: 0 4px;
  font-size: 14px;
  color: var(--text-muted); /* Texte #050080 */
  transition: background 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Page sélectionnée */
.workspaces-paginator .mat-paginator-page.mat-selected {
  background: var(--primary-color); /* Fond bleu #4a90e2 pour la page active */
  border: 1px solid var(--primary-color); /* Bordure bleue assortie */
  color: var(--text-color); /* Texte blanc #ffffff pour contraste */
}

/* Icônes des boutons (précédent/suivant) */
.workspaces-paginator .mat-paginator-icon {
  color: var(--primary-color); /* Icônes bleues #4a90e2 */
}

.workspaces-paginator .mat-paginator-navigation-previous:hover .mat-paginator-icon,
.workspaces-paginator .mat-paginator-navigation-next:hover .mat-paginator-icon {
  color: white; /* Icônes blanches #ffffff au survol */
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */
@media (max-width: 768px) {
  .workspaces-container {
    grid-template-columns: 1fr;
  }

  .no-workspaces-card {
    grid-column: span 1;
  }

  .workspaces-title {
    font-size: 24px;
  }

  .workspace-name {
    font-size: 14px;
  }

  .workspace-description {
    font-size: 13px;
  }

  .workspace-detail {
    font-size: 12px;
  }

  .detail-icon {
    font-size: 14px;
    width: 14px;
    height: 14px;
  }

  .workspaces-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-bar {
    width: 100%;
    margin-top: 12px;
  }
}
