/* Style pour le message de succès */
.workspace-success-message {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #4caf50;
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  animation: slideIn 0.3s ease, fadeOut 0.3s ease 2.7s;
}

.workspace-success-message .icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.workspace-success-message .icon i {
  font-size: 24px;
}

.workspace-success-message .message {
  font-size: 16px;
  font-weight: 500;
}

@keyframes slideIn {
  from {
    transform: translateX(100px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Variables CSS */
:root {
  --primary-color: #001660;
  --primary-light: #2a3f8a;
  --primary-dark: #00104d;
  --accent-color: #F59E0B;
  --accent-light: #FBBF24;
  --accent-dark: #D97706;
  --text-primary: #1E293B;
  --text-secondary: #475569;
  --text-light: #F8FAFC;
  --bg-primary: #F8FAFC;
  --bg-secondary: #F1F5F9;
  --bg-card: #FFFFFF;
  --border-light: #E2E8F0;
  --border-medium: #CBD5E0;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.05), 0 10px 10px rgba(0, 0, 0, 0.02);
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --transition-slow: 0.35s ease;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', sans-serif; /* Switched to Roboto for a more formal, professional look */
  background: #f7f9fc; /* Slightly lighter and more neutral background */
  color: #2d3748; /* Darker gray for better readability */
}

/* Navbar */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 40px;
  background: #ffffff;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
}

.logo {
  font-size: 22px;
  font-weight: 700;
  color: #1a2e5b; /* Darker, more professional blue */
  text-transform: uppercase;
  letter-spacing: 0.8px;
}

.nav-links a {
  margin-left: 28px;
  text-decoration: none;
  color: #4a5568;
  font-weight: 500;
  font-size: 14px;
  transition: color 0.2s ease;
}

.nav-links a:hover {
  color: #1a2e5b;
}

/* Form Container with Premium Background */
.workspace-form {
  padding: 80px 24px 40px;
  display: flex;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4f8 0%, #e6ecf5 100%), repeating-linear-gradient(45deg, rgba(0, 22, 96, 0.02) 0px, rgba(0, 22, 96, 0.02) 1px, transparent 1px, transparent 10px), repeating-linear-gradient(135deg, rgba(245, 158, 11, 0.02) 0px, rgba(245, 158, 11, 0.02) 1px, transparent 1px, transparent 10px);
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
  border-top: 3px solid #001660; /* Bordure supérieure bleu marine */
}

/* Decorative Elements */
.workspace-form::before {
  content: '';
  position: absolute;
  top: -10%;
  right: -5%;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 22, 96, 0.05) 0%, rgba(0, 22, 96, 0.02) 100%);
  z-index: 0;
}

.workspace-form::after {
  content: '';
  position: absolute;
  bottom: -10%;
  left: -5%;
  width: 250px;
  height: 250px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%);
  z-index: 0;
}

/* Premium Workspace Card with Glassmorphism Effect */
.workspace-card {
  width: 100%;
  max-width: 900px;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.85); /* Fond semi-transparent */
  backdrop-filter: blur(10px); /* Effet de flou */
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 22px 35px rgba(0, 22, 96, 0.07),
  0 8px 15px rgba(0, 22, 96, 0.05),
  0 1px 2px rgba(0, 22, 96, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-top: 3px solid #001660; /* Bordure supérieure bleu marine */
  border-bottom: 3px solid #F59E0B; /* Bordure inférieure orange */
  transition: all var(--transition-normal);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.workspace-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

/* Header */
.header-center {
  text-align: center;
  padding: 25px 0;
  /*background: var(--bg-light);*/
  border-bottom: 1px solid var(--border-light);
  position: relative;
}

/* Accent line under header */
.header-center::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: var(--primary-color);
  border-radius: 1px;
}

/* Accents latéraux */
.header-center .left-accent,
.header-center .right-accent {
  position: absolute;
  top: 50%;
  width: 3px;
  height: 30px;
  border-radius: 1.5px;
  opacity: 0.7;
  transform: translateY(-50%);
  animation: fadeInOut 3s infinite alternate;
}

.header-center .left-accent {
  left: 20px;
  background: linear-gradient(to bottom, #001660, #F59E0B);
  animation-delay: 0.5s;
}

.header-center .right-accent {
  right: 20px;
  background: linear-gradient(to bottom, #F59E0B, #001660);
  animation-delay: 1s;
}

@keyframes fadeInOut {
  0% {
    opacity: 0.5;
    height: 25px;
  }
  100% {
    opacity: 0.8;
    height: 35px;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.mat-card-title {
  font-family: 'Poppins', sans-serif;
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #001660; /* Bleu marine */
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.9);
  position: relative;
  padding: 0 15px;
}

/* Effet de soulignement au survol du titre */
.mat-card-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #001660, #F59E0B);
  transition: all 0.3s ease;
  transform: translateX(-50%);
  opacity: 0;
}

.mat-card-title:hover::after {
  width: 80%;
  opacity: 1;
}

.workspace-icon {
  font-size: 32px;
  margin-right: 12px;
  color: #001660; /* Bleu marine */
  background: linear-gradient(135deg, #001660, #F59E0B); /* Dégradé bleu-orange */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
  position: relative;
  animation: colorShift 8s infinite alternate;
}

@keyframes colorShift {
  0% {
    background: linear-gradient(135deg, #001660, #F59E0B);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  50% {
    background: linear-gradient(135deg, #001660, #2a3f8a, #F59E0B);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  100% {
    background: linear-gradient(135deg, #F59E0B, #001660);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Card Content with Premium Styling */
.card-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 32px;
  background: var(--bg-card);
}

.field-group {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

/* Animation for Form Fields */
.animate-field {
  animation: fadeInUp 0.5s ease forwards;
  opacity: 0;
  transform: translateY(10px);
}

.animate-field:nth-child(1) {
  animation-delay: 0.1s;
}

.animate-field:nth-child(2) {
  animation-delay: 0.2s;
}

.animate-field:nth-child(3) {
  animation-delay: 0.3s;
}

.animate-field:nth-child(4) {
  animation-delay: 0.4s;
}

.animate-field:nth-child(5) {
  animation-delay: 0.5s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card Animation */
.animated-card {
  animation: fadeIn 0.6s ease forwards;
  opacity: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Form Fields with Enhanced Styling */
.form-field {
  flex: 1;
  min-width: 280px;
  position: relative;
}

.form-field .mat-mdc-form-field {
  width: 100%;
  background: var(--bg-card);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

.form-field:hover .mat-mdc-form-field {
  box-shadow: var(--shadow-sm);
}

.form-field .mat-mdc-form-field input,
.form-field .mat-mdc-form-field textarea {
  font-size: 15px;
  color: var(--text-primary);
  font-weight: 400;
  padding: 4px 0;
}

.form-field .mat-mdc-form-field textarea {
  resize: vertical;
  min-height: 80px;
}

.form-field .mat-mdc-form-field .mat-mdc-text-field-wrapper {
  background: rgba(248, 250, 252, 0.8);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  border-left: 3px solid #001660; /* Bordure gauche bleu marine */
  transition: all var(--transition-normal);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.form-field:hover .mat-mdc-form-field .mat-mdc-text-field-wrapper {
  border-color: var(--border-medium);
  border-left: 3px solid #F59E0B; /* Bordure gauche orange au survol */
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 12px rgba(0, 22, 96, 0.05),
  0 2px 6px rgba(245, 158, 11, 0.08); /* Ombre combinée bleu et orange */
  transform: translateY(-2px);
}

/* Effet de focus sur les champs */
.form-field .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {
  border-color: #001660;
  border-left: 3px solid #F59E0B;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 12px rgba(0, 22, 96, 0.08),
  0 2px 6px rgba(245, 158, 11, 0.1);
  transform: translateY(-2px);
}

.form-field .mat-mdc-form-field .mat-mdc-form-field-flex {
  padding: 4px 12px;
}

.form-field .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {
  padding-top: 6px;
}

.form-field .mat-mdc-form-field mat-label {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 14px;
  letter-spacing: 0.2px;
}

.form-field .mat-mdc-form-field mat-error {
  font-size: 12px;
  color: #DC2626;
  margin-top: 4px;
}

.form-field .mat-mdc-form-field mat-hint {
  font-size: 12px;
  color: #94A3B8; /* Couleur de texte atténuée */
  margin-top: 4px;
}

.form-field .mat-mdc-form-field mat-icon {
  color: var(--primary-color);
  font-size: 20px;
}

/* Premium Logo Upload Field */
.logo-upload-field {
  flex: 1;
  min-width: 280px;
  position: relative;
}

.upload-label {
  display: block;
  font-weight: 600;
  font-size: 14px;
  color: var(--primary-color);
  margin-bottom: 10px;
  letter-spacing: 0.2px;
}

.logo-upload-field input[type="file"] {
  width: 100%;
  padding: 12px 16px;
  font-size: 14px;
  border: 2px dashed #001660; /* Bordure en pointillés bleu marine */
  border-radius: var(--radius-md);
  background: rgba(248, 250, 252, 0.8);
  cursor: pointer;
  transition: all var(--transition-normal);
  color: var(--text-secondary);
  position: relative;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.logo-upload-field input[type="file"]:hover {
  border-color: #F59E0B; /* Bordure en pointillés orange au survol */
  background: rgba(245, 158, 11, 0.05);
  box-shadow: 0 4px 12px rgba(0, 22, 96, 0.05),
  0 2px 6px rgba(245, 158, 11, 0.08);
  transform: translateY(-2px);
}

.logo-upload-field mat-hint {
  font-size: 12px;
  color: var(--text-muted);
  margin-top: 8px;
  display: block;
}

.logo-preview {
  margin-top: 16px;
  position: relative;
  display: inline-block;
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 22, 96, 0.08),
  0 2px 6px rgba(0, 0, 0, 0.05);
  transition: all var(--transition-normal);
  border: 2px solid #FFFFFF;
  background: rgba(255, 255, 255, 0.8);
  padding: 3px;
}

.logo-preview::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #001660, transparent, #F59E0B, transparent, #001660);
  background-size: 400% 400%;
  z-index: -1;
  animation: gradientBorder 6s ease infinite;
  border-radius: var(--radius-md);
}

@keyframes gradientBorder {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.logo-preview:hover {
  transform: scale(1.05) rotate(1deg);
  box-shadow: 0 8px 20px rgba(0, 22, 96, 0.1),
  0 4px 10px rgba(245, 158, 11, 0.08);
}

.logo-preview .preview-image {
  max-width: 120px;
  max-height: 120px;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  object-fit: contain;
  background: white;
}

.logo-preview .remove-logo-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background: white;
  border-radius: 50%;
  box-shadow: var(--shadow-md);
  color: #DC2626;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.logo-preview .remove-logo-btn:hover {
  background: #FEE2E2;
  transform: scale(1.1);
}

.logo-preview .remove-logo-btn mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Premium Action Buttons */
.center-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(248, 250, 252, 0.9));
  border-top: 1px solid rgba(226, 232, 240, 0.5);
  position: relative;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* Accent line above actions */
.center-actions::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 22, 96, 0.1), rgba(245, 158, 11, 0.1), rgba(0, 22, 96, 0.1), transparent);
}

.cancel-btn {
  padding: 10px 24px;
  font-size: 15px;
  font-weight: 500;
  border-radius: var(--radius-md);
  border: 1px solid rgba(203, 213, 225, 0.8);
  color: #475569; /* Gris foncé */
  background: rgba(255, 255, 255, 0.8);
  transition: all var(--transition-normal);
  letter-spacing: 0.3px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.cancel-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 22, 96, 0.05), transparent);
  transition: all 0.6s ease;
}

.cancel-btn:hover {
  background: rgba(248, 250, 252, 0.9);
  border-color: #001660; /* Bordure bleu marine au survol */
  color: #001660; /* Texte bleu marine au survol */
  box-shadow: 0 4px 12px rgba(0, 22, 96, 0.05),
  0 2px 4px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.cancel-btn:hover::before {
  left: 100%;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 28px;
  font-size: 15px;
  font-weight: 500;
  border-radius: var(--radius-md);
  background: linear-gradient(135deg, #001660, #2a3f8a); /* Dégradé bleu marine */
  color: white;
  border: none;
  border-bottom: 3px solid #F59E0B; /* Bordure inférieure orange */
  transition: all var(--transition-normal);
  box-shadow: 0 4px 12px rgba(0, 22, 96, 0.1),
  0 2px 4px rgba(0, 22, 96, 0.1);
  letter-spacing: 0.3px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.add-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.add-btn:hover {
  background: linear-gradient(135deg, #2a3f8a, #001660); /* Dégradé bleu marine inversé */
  box-shadow: 0 8px 20px rgba(0, 22, 96, 0.15),
  0 4px 10px rgba(245, 158, 11, 0.1);
  transform: translateY(-3px);
  border-bottom: 3px solid #D97706; /* Bordure inférieure orange foncé */
}

.add-btn:hover::before {
  left: 100%;
}

.add-btn:disabled {
  background: linear-gradient(135deg, #A0AEC0, #CBD5E0);
  color: #E2E8F0;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Animations for a Professional Look */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.add-btn mat-icon {
  font-size: 20px;
}

.add-btn mat-spinner {
  margin-right: 8px;
}

/* Responsive Design with Premium Adjustments */
@media (max-width: 1024px) {
  .workspace-form {
    padding: 72px 20px 32px;
  }

  .workspace-card {
    max-width: 800px;
  }

  .mat-card-title {
    font-size: 26px;
  }

  .workspace-icon {
    font-size: 30px;
  }
}

@media (max-width: 768px) {
  .workspace-form {
    padding: 60px 16px 24px;
  }

  .card-content {
    padding: 24px 20px;
    gap: 20px;
  }

  .field-group {
    flex-direction: column;
    gap: 16px;
  }

  .form-field,
  .logo-upload-field {
    min-width: 100%;
  }

  .mat-card-title {
    font-size: 24px;
  }

  .workspace-icon {
    font-size: 28px;
  }

  .center-actions {
    flex-direction: column-reverse;
    padding: 20px;
  }

  .add-btn,
  .cancel-btn {
    width: 100%;
    padding: 12px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .workspace-form {
    padding: 48px 12px 20px;
  }

  .card-content {
    padding: 20px 16px;
    gap: 16px;
  }

  .mat-card-title {
    font-size: 22px;
  }

  .workspace-icon {
    font-size: 26px;
  }

  .header-center {
    padding: 24px 0;
  }

  .header-center::after {
    width: 80px;
  }

  .form-field .mat-mdc-form-field input,
  .form-field .mat-mdc-form-field textarea {
    font-size: 14px;
  }

  .upload-label {
    font-size: 13px;
  }

  .logo-upload-field input[type="file"] {
    padding: 10px 14px;
    font-size: 13px;
  }

  .logo-preview .preview-image {
    max-width: 100px;
    max-height: 100px;
  }
}

.navbar {
  padding: 12px 20px;
}

.nav-links a {
  margin-left: 16px;
  font-size: 13px;
}



@media (max-width: 480px) {
  .mat-card-title {
    font-size: 20px;
  }

  .workspace-icon {
    font-size: 24px;
  }

  .logo {
    font-size: 18px;
  }

  .nav-links a {
    margin-left: 12px;
    font-size: 12px;
  }
}
