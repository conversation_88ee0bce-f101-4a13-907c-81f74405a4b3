@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary-color: #f9fafb;
  --text-color: #1f2937;
  --text-muted: #6b7280;
  --sidebar-bg: #ffffff;
  --active-bg: #e0eaff;
  --hover-bg: #f3f4f6;
  --danger-color: #dc2626;
  --card-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
  --security-gradient: linear-gradient(135deg, #facc15 0%, #d97706 100%);
  --border-radius: 12px;
}

.tab-content {
  padding: 40px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
  min-height: calc(100vh - 100px);
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.section-title {
  font-family: 'Inter', sans-serif;
  font-size: 28px;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 12px;
  text-align: center;
  position: relative;
  padding-bottom: 8px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--security-gradient);
  border-radius: 2px;
}

.section-subtitle {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: var(--text-muted);
  text-align: center;
  margin-bottom: 32px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* Settings Card */
.settings-card {
  background: #ffffff;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  padding: 24px;
  margin-bottom: 30px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.settings-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.settings-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: var(--security-gradient);
  transition: width 0.3s ease;
}

.settings-card:hover::before {
  width: 8px;
}

/* Card Headers */
mat-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

mat-card-title {
  font-family: 'Inter', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

mat-card-title::before {
  content: '🔒';
  font-size: 18px;
}

/* Card Content */
mat-card-content {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: var(--text-muted);
}

/* Form Elements */
mat-form-field {
  width: 100%;
  margin-bottom: 20px;
}

mat-form-field .mat-form-field-label {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-muted);
}

input[matInput] {
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  color: var(--text-color);
  border-radius: 8px;
}

mat-slide-toggle {
  display: block;
  margin-bottom: 20px;
  color: var(--text-color);
}

mat-slide-toggle .mat-slide-toggle-bar {
  border-radius: 16px;
  background: #e5e7eb;
}

mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
  background: var(--security-gradient);
}

.info-text {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: var(--text-muted);
  margin-top: 12px;
  padding-left: 8px;
  border-left: 4px solid #facc15;
  background: #fffbeb;
  border-radius: 4px;
  padding: 8px;
}

.two-factor-setup {
  margin-top: 16px;
  padding: 20px;
  background: var(--secondary-color);
  border-radius: var(--border-radius);
  border-left: 6px solid #d97706;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Buttons */
button[mat-raised-button] {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 600;
  padding: 10px 24px;
  border-radius: var(--border-radius);
  background: var(--security-gradient);
  color: white;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  justify-content: center;
  margin-top: 8px;
}

button[mat-raised-button]:hover:not(:disabled) {
  background: linear-gradient(135deg, #e0a800 0%, #b45309 100%);
  box-shadow: 0 6px 18px rgba(217, 119, 6, 0.3);
  transform: translateY(-2px);
}

button[mat-button][color="warn"] {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--danger-color);
  background: #fee2e2;
  padding: 8px 16px;
  border-radius: var(--border-radius);
  transition: background 0.3s ease;
  margin-top: 8px;
}

button[mat-button][color="warn"]:hover:not(:disabled) {
  background: #fecaca;
}

button[mat-button][color="accent"] {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--primary-color);
  background: #dbeafe;
  padding: 8px 16px;
  border-radius: var(--border-radius);
  margin-left: 8px;
  transition: background 0.3s ease;
}

button[mat-button][color="accent"]:hover:not(:disabled) {
  background: #bfdbfe;
}

/* Spinner */
mat-spinner {
  margin-left: 8px;
}

/* Table Styling */
table {
  width: 100%;
  margin-top: 16px;
  border-collapse: separate;
  border-spacing: 0;
  background: #ffffff;
  box-shadow: var(--card-shadow);
  border-radius: var(--border-radius);
  overflow: hidden;
}

th, td {
  padding: 14px 18px;
  text-align: left;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: var(--text-color);
}

th {
  font-weight: 600;
  background: #f3f4f6;
  border-bottom: 2px solid #e5e7eb;
}

tr {
  transition: background 0.3s ease;
}

tr:hover {
  background: #f9fafb;
}

td button {
  margin-top: 0;
  width: auto;
}

/* History List */
.history-list {
  list-style: none;
  padding: 0;
  margin-top: 16px;
}

.history-list li {
  padding: 12px 16px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: var(--text-muted);
  border-bottom: 1px solid #e5e7eb;
  transition: background 0.3s ease;
}

.history-list li:hover {
  background: #f3f4f6;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .section-title { font-size: 24px; }
  .section-subtitle { font-size: 15px; }
  .settings-card { padding: 20px; }
  mat-card-title { font-size: 17px; }
  mat-card-content { font-size: 13px; }
  mat-form-field { margin-bottom: 16px; }
}

@media (max-width: 768px) {
  .tab-content { padding: 20px 10px; }
  .settings-card { padding: 16px; }
  .two-factor-setup { padding: 16px; }
  button[mat-raised-button] { padding: 8px 20px; }
  th, td { padding: 12px 14px; }
}

@media (max-width: 480px) {
  .section-title { font-size: 20px; }
  .section-subtitle { font-size: 14px; }
  .settings-card { padding: 12px; }
  mat-card-title { font-size: 16px; }
  mat-card-content { font-size: 12px; }
  button[mat-raised-button] { padding: 6px 16px; font-size: 13px; }
  button[mat-button][color="warn"], button[mat-button][color="accent"] { padding: 6px 12px; font-size: 13px; }
  th, td { padding: 10px 12px; font-size: 13px; }
  .history-list li { padding: 10px 12px; }
}
