/* === Dark Elegant Variables === */
:root {
  /* Couleurs principales sombres */
  --dark-primary: #0a0a0f;
  --dark-secondary: #1a1a2e;
  --dark-tertiary: #16213e;
  --dark-accent: #0f3460;
  
  /* Couleurs d'accent élégantes */
  --gold-primary: #d4af37;
  --gold-light: #f4d03f;
  --gold-dark: #b7950b;
  --copper: #b87333;
  --bronze: #cd7f32;
  
  /* Couleurs néon sophistiquées */
  --neon-blue: #00d4ff;
  --neon-purple: #8a2be2;
  --neon-cyan: #00ffff;
  --electric-blue: #0066ff;
  
  /* Couleurs de texte */
  --text-primary: #ffffff;
  --text-secondary: #e8e8e8;
  --text-muted: #a0a0a0;
  --text-accent: var(--gold-primary);
  
  /* Backgrounds sombres */
  --bg-primary: rgba(10, 10, 15, 0.95);
  --bg-secondary: rgba(26, 26, 46, 0.9);
  --bg-glass: rgba(22, 33, 62, 0.8);
  --bg-overlay: rgba(0, 0, 0, 0.85);
  
  /* Bordures et effets */
  --border-gold: rgba(212, 175, 55, 0.3);
  --border-neon: rgba(0, 212, 255, 0.4);
  --glow-gold: 0 0 20px rgba(212, 175, 55, 0.5);
  --glow-blue: 0 0 25px rgba(0, 212, 255, 0.6);
  --glow-purple: 0 0 30px rgba(138, 43, 226, 0.5);
  
  /* Dégradés sophistiqués */
  --gradient-dark: linear-gradient(135deg, var(--dark-primary) 0%, var(--dark-secondary) 50%, var(--dark-tertiary) 100%);
  --gradient-gold: linear-gradient(135deg, var(--gold-dark) 0%, var(--gold-primary) 50%, var(--gold-light) 100%);
  --gradient-neon: linear-gradient(135deg, var(--neon-blue) 0%, var(--electric-blue) 50%, var(--neon-purple) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.02) 100%);
  
  /* Ombres élégantes */
  --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.6);
  --shadow-gold: 0 8px 32px rgba(212, 175, 55, 0.3);
  --shadow-neon: 0 8px 32px rgba(0, 212, 255, 0.4);
  --shadow-deep: 0 20px 60px rgba(0, 0, 0, 0.8);
}

/* === Global Reset === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* === Message Overlay === */
.message-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(25px) saturate(200%);
  animation: overlayFadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px) saturate(100%);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(25px) saturate(200%);
  }
}

/* === Message Container === */
.message-container {
  background: var(--gradient-dark);
  backdrop-filter: blur(50px) saturate(200%);
  border: 2px solid var(--border-gold);
  border-radius: 28px;
  box-shadow: 
    var(--shadow-deep),
    var(--glow-gold),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  max-width: 580px;
  width: 92%;
  margin: 2rem;
  overflow: hidden;
  position: relative;
  animation: containerSlideUp 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes containerSlideUp {
  from {
    transform: translateY(80px) scale(0.85) rotateX(15deg);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1) rotateX(0deg);
    opacity: 1;
  }
}

/* === Background Effects === */
.bg-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: -1;
}

.floating-orb {
  position: absolute;
  border-radius: 50%;
  opacity: 0.7;
  filter: blur(2px);
}

.orb-1 {
  width: 150px;
  height: 150px;
  background: var(--gradient-gold);
  top: -75px;
  right: -75px;
  animation: floatOrb1 12s ease-in-out infinite;
  box-shadow: var(--glow-gold);
}

.orb-2 {
  width: 100px;
  height: 100px;
  background: var(--gradient-neon);
  bottom: -50px;
  left: -50px;
  animation: floatOrb2 8s ease-in-out infinite reverse;
  box-shadow: var(--glow-blue);
}

.orb-3 {
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, var(--neon-purple) 0%, var(--electric-blue) 100%);
  top: 60%;
  left: 60%;
  transform: translate(-50%, -50%);
  animation: floatOrb3 15s ease-in-out infinite;
  box-shadow: var(--glow-purple);
}

@keyframes floatOrb1 {
  0%, 100% { transform: translate(0, 0) scale(1) rotate(0deg); }
  33% { transform: translate(-30px, 15px) scale(1.2) rotate(120deg); }
  66% { transform: translate(15px, -25px) scale(0.8) rotate(240deg); }
}

@keyframes floatOrb2 {
  0%, 100% { transform: translate(0, 0) scale(1) rotate(0deg); }
  50% { transform: translate(25px, -30px) scale(1.3) rotate(180deg); }
}

@keyframes floatOrb3 {
  0%, 100% { transform: translate(-50%, -50%) scale(1) rotate(0deg); }
  25% { transform: translate(-70%, -30%) scale(1.1) rotate(90deg); }
  75% { transform: translate(-30%, -70%) scale(0.9) rotate(270deg); }
}

/* === Message Content === */
.message-content {
  padding: 3.5rem 3rem 3rem;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
  position: relative;
}

/* === Icon Section === */
.icon-section {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  border-radius: 25px;
  background: var(--gradient-gold);
  box-shadow: 
    var(--shadow-gold),
    var(--glow-gold),
    inset 0 2px 4px rgba(255, 255, 255, 0.2);
  animation: iconPulse 3s ease-in-out infinite;
  border: 1px solid var(--border-gold);
}

.main-icon {
  color: var(--dark-primary);
  font-size: 50px;
  width: 50px;
  height: 50px;
  z-index: 2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.icon-glow {
  position: absolute;
  top: -15px;
  left: -15px;
  right: -15px;
  bottom: -15px;
  background: var(--gradient-gold);
  border-radius: 35px;
  opacity: 0.4;
  filter: blur(20px);
  animation: glowPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { 
    transform: scale(1) rotate(0deg); 
    box-shadow: var(--shadow-gold), var(--glow-gold);
  }
  50% { 
    transform: scale(1.08) rotate(5deg); 
    box-shadow: var(--shadow-gold), 0 0 40px rgba(212, 175, 55, 0.8);
  }
}

@keyframes glowPulse {
  0%, 100% { 
    opacity: 0.4; 
    transform: scale(1) rotate(0deg); 
  }
  50% { 
    opacity: 0.8; 
    transform: scale(1.2) rotate(180deg); 
  }
}

/* === Text Section === */
.text-section {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--text-primary);
  margin: 0;
  letter-spacing: -0.02em;
  line-height: 1.1;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  animation: titleSlideIn 1s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
  background: var(--gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-underline {
  width: 80px;
  height: 5px;
  background: var(--gradient-gold);
  margin: 0 auto;
  border-radius: 3px;
  position: relative;
  overflow: hidden;
  animation: underlineExpand 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.5s both;
  box-shadow: var(--glow-gold);
}

.title-underline::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.9), transparent);
  animation: shimmerSlide 2.5s ease-in-out infinite 1.5s;
}

@keyframes titleSlideIn {
  from {
    transform: translateY(30px) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes underlineExpand {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 80px;
    opacity: 1;
  }
}

@keyframes shimmerSlide {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* === Main Message === */
.main-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  animation: messageSlideIn 1s cubic-bezier(0.4, 0, 0.2, 1) 0.6s both;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.message-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: var(--gradient-neon);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--glow-blue);
  animation: messageIconSpin 4s ease-in-out infinite;
}

.message-icon mat-icon {
  color: var(--dark-primary);
  font-size: 28px;
  width: 28px;
  height: 28px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes messageSlideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes messageIconSpin {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

/* === Sub Message === */
.sub-message {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
  animation: subMessageFadeIn 1s cubic-bezier(0.4, 0, 0.2, 1) 0.8s both;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  text-align: center;
}

@keyframes subMessageFadeIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* === Progress Section === */
.progress-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  animation: progressSlideIn 1s cubic-bezier(0.4, 0, 0.2, 1) 1s both;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.progress-fill {
  height: 100%;
  background: var(--gradient-neon);
  border-radius: 4px;
  width: 0%;
  animation: progressFill 4s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  position: relative;
  box-shadow: var(--glow-blue);
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: progressShimmer 2s ease-in-out infinite;
}

.progress-text {
  font-size: 0.95rem;
  color: var(--text-muted);
  text-align: center;
  margin: 0;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  animation: textPulse 2s ease-in-out infinite;
}

@keyframes progressFill {
  0% { width: 0%; }
  50% { width: 85%; }
  100% { width: 0%; }
}

@keyframes progressShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes progressSlideIn {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes textPulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* === Action Section === */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  animation: actionSlideIn 1s cubic-bezier(0.4, 0, 0.2, 1) 1.2s both;
}

.primary-button {
  background: var(--gradient-gold);
  color: var(--dark-primary);
  border: none;
  border-radius: 15px;
  padding: 1.2rem 2rem;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    var(--shadow-gold),
    var(--glow-gold);
  position: relative;
  overflow: hidden;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.primary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.primary-button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 
    0 15px 35px rgba(212, 175, 55, 0.4),
    0 0 50px rgba(212, 175, 55, 0.6);
}

.primary-button:hover::before {
  left: 100%;
}

.primary-button:active {
  transform: translateY(-1px) scale(1.01);
}

.secondary-button {
  background: rgba(0, 212, 255, 0.1);
  backdrop-filter: blur(20px);
  color: var(--text-primary);
  border: 1px solid var(--border-neon);
  border-radius: 15px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.secondary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.secondary-button:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: translateY(-2px) scale(1.01);
  box-shadow: 
    var(--shadow-neon),
    var(--glow-blue);
  border-color: var(--neon-blue);
}

.secondary-button:hover::before {
  left: 100%;
}

.secondary-button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

@keyframes actionSlideIn {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* === Responsive Design === */
@media (max-width: 768px) {
  .message-container {
    margin: 1rem;
    border-radius: 24px;
    max-width: none;
  }

  .message-content {
    padding: 3rem 2rem 2.5rem;
    gap: 2rem;
  }

  .welcome-title {
    font-size: 2rem;
  }

  .icon-wrapper {
    width: 80px;
    height: 80px;
    border-radius: 20px;
  }

  .main-icon {
    font-size: 40px;
    width: 40px;
    height: 40px;
  }

  .main-message {
    font-size: 1.1rem;
    flex-direction: column;
    gap: 0.8rem;
  }

  .message-icon {
    width: 45px;
    height: 45px;
  }

  .message-icon mat-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
  }

  .sub-message {
    font-size: 1rem;
  }

  .action-section {
    gap: 0.8rem;
  }

  .primary-button,
  .secondary-button {
    padding: 1rem 1.5rem;
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .message-container {
    margin: 0.5rem;
    border-radius: 20px;
  }

  .message-content {
    padding: 2.5rem 1.5rem 2rem;
    gap: 1.5rem;
  }

  .welcome-title {
    font-size: 1.75rem;
  }

  .icon-wrapper {
    width: 70px;
    height: 70px;
    border-radius: 18px;
  }

  .main-icon {
    font-size: 35px;
    width: 35px;
    height: 35px;
  }

  .main-message {
    font-size: 1rem;
  }

  .message-icon {
    width: 40px;
    height: 40px;
  }

  .message-icon mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  .sub-message {
    font-size: 0.9rem;
  }

  .primary-button,
  .secondary-button {
    padding: 0.9rem 1.2rem;
    font-size: 0.9rem;
  }
}
