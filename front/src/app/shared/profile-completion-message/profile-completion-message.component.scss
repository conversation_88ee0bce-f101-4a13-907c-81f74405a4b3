/* === Variables === */
:root {
  --primary-color: #001660;
  --success-color: #4CAF50;
  --orange-color: #ff6b00;
  --white: #ffffff;
  --text-color: #2d3748;
  --text-light: #718096;
  --shadow-color: rgba(0, 22, 96, 0.15);
  --overlay-bg: rgba(0, 0, 0, 0.5);
}

/* === Message Overlay === */
.message-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* === Message Container === */
.message-container {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 
    0 20px 60px rgba(0, 22, 96, 0.2),
    0 8px 24px rgba(0, 22, 96, 0.1);
  max-width: 480px;
  width: 90%;
  margin: 2rem;
  overflow: hidden;
  position: relative;
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* === Message Header === */
.message-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, #002080 100%);
  color: var(--white);
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
  position: relative;
}

.success-icon {
  color: var(--success-color);
  font-size: 2rem;
  height: 2rem;
  width: 2rem;
  margin-right: 1rem;
  animation: bounceIn 0.6s ease-out 0.2s both;
}

@keyframes bounceIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.message-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  flex: 1;
}

.close-button {
  color: var(--white);
  position: absolute;
  top: 1rem;
  right: 1rem;
  transition: all 0.3s ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

/* === Message Content === */
.message-content {
  padding: 2rem;
  text-align: center;
}

.main-message {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
  animation: fadeInUp 0.5s ease-out 0.3s both;
}

.info-icon {
  color: var(--orange-color);
  margin-right: 0.5rem;
  font-size: 1.25rem;
  height: 1.25rem;
  width: 1.25rem;
}

.sub-message {
  color: var(--text-light);
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
  animation: fadeInUp 0.5s ease-out 0.4s both;
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* === Message Actions === */
.message-actions {
  padding: 0 2rem 2rem;
  text-align: center;
  animation: fadeInUp 0.5s ease-out 0.5s both;
}

.action-button {
  background: linear-gradient(135deg, var(--primary-color) 0%, #002080 100%);
  color: var(--white);
  border: none;
  border-radius: 8px;
  padding: 0.75rem 2rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 120px;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 8px 20px rgba(0, 22, 96, 0.3),
    0 4px 12px rgba(0, 22, 96, 0.2);
}

.action-button:active {
  transform: translateY(0);
}

/* === Responsive === */
@media (max-width: 768px) {
  .message-container {
    margin: 1rem;
    border-radius: 12px;
  }

  .message-header {
    padding: 1.25rem 1.5rem;
  }

  .message-header h3 {
    font-size: 1.25rem;
  }

  .message-content {
    padding: 1.5rem;
  }

  .main-message {
    font-size: 1rem;
  }

  .sub-message {
    font-size: 0.85rem;
  }

  .message-actions {
    padding: 0 1.5rem 1.5rem;
  }

  .action-button {
    padding: 0.6rem 1.5rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .message-container {
    margin: 0.5rem;
    border-radius: 8px;
  }

  .message-header {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
  }

  .success-icon {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }

  .close-button {
    top: 0.5rem;
    right: 0.5rem;
  }

  .main-message {
    flex-direction: column;
    gap: 0.5rem;
  }

  .info-icon {
    margin-right: 0;
  }
}
