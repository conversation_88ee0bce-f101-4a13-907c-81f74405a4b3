/* === Ultra-Modern Variables === */
:root {
  --primary-color: #001660;
  --primary-light: #0020a0;
  --primary-dark: #001040;
  --accent-color: #ff6b00;
  --accent-light: #ff8c3a;
  --success-color: #00d4aa;
  --warning-color: #ffa726;
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  --text-color: var(--gray-800);
  --text-light: var(--gray-500);
  --overlay-bg: rgba(15, 23, 42, 0.6);
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

/* === Global Reset === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* === Message Overlay === */
.message-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(20px) saturate(180%);
  animation: overlayFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px) saturate(100%);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(20px) saturate(180%);
  }
}

/* === Message Container === */
.message-container {
  background: var(--glass-bg);
  backdrop-filter: blur(40px) saturate(200%);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  box-shadow: var(--shadow-2xl);
  max-width: 520px;
  width: 90%;
  margin: 2rem;
  overflow: hidden;
  position: relative;
  animation: containerSlideUp 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes containerSlideUp {
  from {
    transform: translateY(60px) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* === Background Effects === */
.bg-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: -1;
}

.floating-orb {
  position: absolute;
  border-radius: 50%;
  opacity: 0.6;
  filter: blur(1px);
}

.orb-1 {
  width: 120px;
  height: 120px;
  background: var(--gradient-primary);
  top: -60px;
  right: -60px;
  animation: floatOrb1 8s ease-in-out infinite;
}

.orb-2 {
  width: 80px;
  height: 80px;
  background: var(--gradient-accent);
  bottom: -40px;
  left: -40px;
  animation: floatOrb2 6s ease-in-out infinite reverse;
}

.orb-3 {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--success-color) 0%, #00b894 100%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: floatOrb3 10s ease-in-out infinite;
}

@keyframes floatOrb1 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(-20px, 10px) scale(1.1); }
  66% { transform: translate(10px, -15px) scale(0.9); }
}

@keyframes floatOrb2 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  50% { transform: translate(15px, -20px) scale(1.2); }
}

@keyframes floatOrb3 {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  25% { transform: translate(-60%, -40%) scale(1.1); }
  75% { transform: translate(-40%, -60%) scale(0.9); }
}

/* === Close Button === */
.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: var(--gray-600);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--gray-800);
  transform: scale(1.1) rotate(90deg);
  box-shadow: var(--shadow-lg);
}

.close-button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* === Message Content === */
.message-content {
  padding: 3rem 2.5rem 2.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* === Icon Section === */
.icon-section {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 20px;
  background: var(--gradient-primary);
  box-shadow: var(--shadow-xl);
  animation: iconPulse 2s ease-in-out infinite;
}

.main-icon {
  color: var(--white);
  font-size: 40px;
  width: 40px;
  height: 40px;
  z-index: 2;
}

.icon-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: var(--gradient-primary);
  border-radius: 25px;
  opacity: 0.3;
  filter: blur(15px);
  animation: glowPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes glowPulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.1); }
}

/* === Text Section === */
.text-section {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.welcome-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
  letter-spacing: -0.025em;
  line-height: 1.2;
  animation: titleSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

.title-underline {
  width: 60px;
  height: 4px;
  background: var(--gradient-accent);
  margin: 0 auto;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
  animation: underlineExpand 1s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

.title-underline::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: shimmerSlide 2s ease-in-out infinite 1s;
}

@keyframes titleSlideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes underlineExpand {
  from {
    width: 0;
  }
  to {
    width: 60px;
  }
}

@keyframes shimmerSlide {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* === Status Card === */
.status-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: var(--shadow-md);
  animation: cardSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.6s both;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--gradient-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.status-icon mat-icon {
  color: var(--white);
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.status-content {
  text-align: left;
  flex: 1;
}

.status-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.25rem 0;
}

.status-description {
  font-size: 0.9rem;
  color: var(--text-light);
  margin: 0;
  line-height: 1.4;
}

@keyframes cardSlideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* === Info Section === */
.info-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  animation: infoFadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.8s both;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-item:hover {
  background: rgba(255, 255, 255, 0.5);
  transform: translateX(5px);
  box-shadow: var(--shadow-md);
}

.info-icon {
  color: var(--primary-color);
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.info-item span {
  font-size: 0.9rem;
  color: var(--text-color);
  font-weight: 500;
}

@keyframes infoFadeIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* === Progress Section === */
.progress-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  animation: progressSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) 1s both;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--gray-200);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: 3px;
  width: 0%;
  animation: progressFill 3s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: progressShimmer 1.5s ease-in-out infinite;
}

.progress-text {
  font-size: 0.85rem;
  color: var(--text-light);
  text-align: center;
  margin: 0;
  font-weight: 500;
}

@keyframes progressFill {
  0% { width: 0%; }
  50% { width: 75%; }
  100% { width: 0%; }
}

@keyframes progressShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes progressSlideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* === Action Section === */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  animation: actionSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) 1.2s both;
}

.primary-button {
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.primary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.primary-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.primary-button:hover::before {
  left: 100%;
}

.primary-button:active {
  transform: translateY(0);
}

.secondary-button {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px);
  color: var(--text-color);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.875rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.secondary-button:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.secondary-button mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

@keyframes actionSlideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* === Responsive Design === */
@media (max-width: 768px) {
  .message-container {
    margin: 1rem;
    border-radius: 20px;
    max-width: none;
  }

  .message-content {
    padding: 2.5rem 1.5rem 2rem;
    gap: 1.5rem;
  }

  .welcome-title {
    font-size: 1.75rem;
  }

  .icon-wrapper {
    width: 70px;
    height: 70px;
    border-radius: 18px;
  }

  .main-icon {
    font-size: 35px;
    width: 35px;
    height: 35px;
  }

  .status-card {
    padding: 1.25rem;
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .status-content {
    text-align: center;
  }

  .info-section {
    gap: 0.75rem;
  }

  .action-section {
    gap: 0.5rem;
  }

  .primary-button,
  .secondary-button {
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .message-container {
    margin: 0.5rem;
    border-radius: 16px;
  }

  .message-content {
    padding: 2rem 1.25rem 1.5rem;
    gap: 1.25rem;
  }

  .welcome-title {
    font-size: 1.5rem;
  }

  .close-button {
    top: 15px;
    right: 15px;
    width: 36px;
    height: 36px;
  }

  .icon-wrapper {
    width: 60px;
    height: 60px;
    border-radius: 15px;
  }

  .main-icon {
    font-size: 30px;
    width: 30px;
    height: 30px;
  }

  .status-card {
    padding: 1rem;
  }

  .status-title {
    font-size: 1rem;
  }

  .status-description {
    font-size: 0.85rem;
  }

  .info-item {
    padding: 0.625rem;
  }

  .info-item span {
    font-size: 0.85rem;
  }
}
