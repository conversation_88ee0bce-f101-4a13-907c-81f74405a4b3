import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { SignupStateService } from '../../services/signup-state.service';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-profile-completion-message',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule],
  template: `
    <div 
      *ngIf="showMessage" 
      class="message-overlay"
      [@slideIn]="showMessage ? 'in' : 'out'"
    >
      <div class="message-container">
        <div class="message-header">
          <mat-icon class="success-icon">check_circle</mat-icon>
          <h3>Bienvenue !</h3>
          <button 
            mat-icon-button 
            class="close-button" 
            (click)="closeMessage()"
            aria-label="Fermer"
          >
            <mat-icon>close</mat-icon>
          </button>
        </div>
        
        <div class="message-content">
          <p class="main-message">
            <mat-icon class="info-icon">info</mat-icon>
            Votre profil sera complété dans quelques minutes
          </p>
          <p class="sub-message">
            Nous traitons vos informations pour créer votre profil professionnel. 
            Vous recevrez une notification dès que ce sera terminé.
          </p>
        </div>
        
        <div class="message-actions">
          <button 
            mat-raised-button 
            color="primary" 
            (click)="closeMessage()"
            class="action-button"
          >
            Compris
          </button>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./profile-completion-message.component.scss'],
  animations: [
    trigger('slideIn', [
      state('in', style({ transform: 'translateY(0)', opacity: 1 })),
      state('out', style({ transform: 'translateY(-100%)', opacity: 0 })),
      transition('out => in', [
        animate('0.3s ease-out')
      ]),
      transition('in => out', [
        animate('0.3s ease-in')
      ])
    ])
  ]
})
export class ProfileCompletionMessageComponent implements OnInit, OnDestroy {
  showMessage = false;
  private autoCloseTimer?: number;

  constructor(private signupStateService: SignupStateService) {}

  ngOnInit(): void {
    // Vérifier si le message doit être affiché
    if (this.signupStateService.shouldShowProfileMessage()) {
      this.showMessage = true;
      
      // Auto-fermeture après 10 secondes
      this.autoCloseTimer = window.setTimeout(() => {
        this.closeMessage();
      }, 10000);
    }
  }

  ngOnDestroy(): void {
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
    }
  }

  closeMessage(): void {
    this.showMessage = false;
    this.signupStateService.markProfileMessageShown();
    
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
    }
  }
}
