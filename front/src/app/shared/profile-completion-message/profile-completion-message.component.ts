import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { SignupStateService } from '../../services/signup-state.service';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-profile-completion-message',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule],
  template: `
    <div
      *ngIf="showMessage"
      class="message-overlay"
      [@slideIn]="showMessage ? 'in' : 'out'"
    >
      <div class="message-container">
        <!-- Background Effects -->
        <div class="bg-effects">
          <div class="floating-orb orb-1"></div>
          <div class="floating-orb orb-2"></div>
          <div class="floating-orb orb-3"></div>
        </div>

        <!-- Close Button -->
        <button
          class="close-button"
          (click)="closeMessage()"
          aria-label="Fermer"
        >
          <mat-icon>close</mat-icon>
        </button>

        <!-- Main Content -->
        <div class="message-content">
          <!-- Icon Section -->
          <div class="icon-section">
            <div class="icon-wrapper">
              <mat-icon class="main-icon">verified_user</mat-icon>
              <div class="icon-glow"></div>
            </div>
          </div>

          <!-- Text Section -->
          <div class="text-section">
            <h2 class="welcome-title">Bienvenue sur Kairos IT !</h2>
            <div class="title-underline"></div>

            <div class="status-card">
              <div class="status-icon">
                <mat-icon>schedule</mat-icon>
              </div>
              <div class="status-content">
                <h3 class="status-title">Génération automatique de votre CV</h3>
                <p class="status-description">
                  Notre IA crée votre profil professionnel à partir de vos informations
                </p>
              </div>
            </div>

            <div class="info-section">
              <div class="info-item">
                <mat-icon class="info-icon">psychology</mat-icon>
                <span>Analyse intelligente de votre CV</span>
              </div>
              <div class="info-item">
                <mat-icon class="info-icon">auto_awesome</mat-icon>
                <span>Optimisation automatique du profil</span>
              </div>
              <div class="info-item">
                <mat-icon class="info-icon">notifications_active</mat-icon>
                <span>Notification dès que c'est prêt</span>
              </div>
            </div>
          </div>

          <!-- Progress Section -->
          <div class="progress-section">
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
            <p class="progress-text">Traitement en cours...</p>
          </div>

          <!-- Action Section -->
          <div class="action-section">
            <button
              class="primary-button"
              (click)="acknowledgeMessage()"
            >
              <span>Parfait, j'ai compris</span>
              <mat-icon>check</mat-icon>
            </button>

            <button
              class="secondary-button"
              (click)="exploreWhileWaiting()"
            >
              <mat-icon>explore</mat-icon>
              <span>Explorer en attendant</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./profile-completion-message.component.scss'],
  animations: [
    trigger('slideIn', [
      state('in', style({ transform: 'translateY(0)', opacity: 1 })),
      state('out', style({ transform: 'translateY(-100%)', opacity: 0 })),
      transition('out => in', [
        animate('0.3s ease-out')
      ]),
      transition('in => out', [
        animate('0.3s ease-in')
      ])
    ])
  ]
})
export class ProfileCompletionMessageComponent implements OnInit, OnDestroy {
  showMessage = false;
  private autoCloseTimer?: number;

  constructor(private signupStateService: SignupStateService) {}

  ngOnInit(): void {
    console.log('ProfileCompletionMessage - ngOnInit called');

    // Vérifier si le message doit être affiché
    const shouldShow = this.signupStateService.shouldShowProfileMessage();
    console.log('Should show profile message:', shouldShow);

    // Vérifier les valeurs dans localStorage pour debug
    const signupCompleted = localStorage.getItem('signup_completed');
    console.log('LocalStorage - signup_completed:', signupCompleted);
    console.log('Note: Le message s\'affiche maintenant à chaque nouvelle inscription');

    if (shouldShow) {
      console.log('Showing profile completion message - PERMANENT DISPLAY');
      this.showMessage = true;

      // SUPPRIMÉ: Auto-fermeture désactivée - le message reste toujours affiché
      // this.autoCloseTimer = window.setTimeout(() => {
      //   this.closeMessage();
      // }, 10000);
    } else {
      console.log('Not showing profile completion message');
    }
  }

  ngOnDestroy(): void {
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
    }
  }

  closeMessage(): void {
    console.log('closeMessage called - hiding message and marking as shown');
    // Fermer le message
    this.showMessage = false;
    // Marquer le message comme affiché pour qu'il ne se répète pas
    this.signupStateService.markProfileMessageShown();
    console.log('Message fermé - ne s\'affichera plus pour cet utilisateur');

    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
    }
  }

  acknowledgeMessage(): void {
    console.log('User acknowledged the message - closing message');
    // Fermer le message quand l'utilisateur clique "Parfait, j'ai compris"
    this.closeMessage();
  }

  exploreWhileWaiting(): void {
    console.log('User wants to explore while waiting - closing message');
    // Fermer le message quand l'utilisateur clique "Explorer en attendant"
    this.closeMessage();
  }
}
