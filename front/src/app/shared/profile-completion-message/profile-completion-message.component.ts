import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { SignupStateService } from '../../services/signup-state.service';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-profile-completion-message',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule],
  template: `
    <div
      *ngIf="showMessage"
      class="message-overlay"
      [@slideIn]="showMessage ? 'in' : 'out'"
    >
      <div class="message-container">
        <!-- Background Effects -->
        <div class="bg-effects">
          <div class="floating-orb orb-1"></div>
          <div class="floating-orb orb-2"></div>
          <div class="floating-orb orb-3"></div>
        </div>

        <!-- Close Button -->
        <button
          class="close-button"
          (click)="closeMessage()"
          aria-label="Fermer"
        >
          <mat-icon>close</mat-icon>
        </button>

        <!-- Main Content -->
        <div class="message-content">
          <!-- Icon Section -->
          <div class="icon-section">
            <div class="icon-wrapper">
              <mat-icon class="main-icon">verified_user</mat-icon>
              <div class="icon-glow"></div>
            </div>
          </div>

          <!-- Text Section -->
          <div class="text-section">
            <h2 class="welcome-title">Bienvenue sur Kairos IT !</h2>
            <div class="title-underline"></div>

            <div class="status-card">
              <div class="status-icon">
                <mat-icon>smart_toy</mat-icon>
              </div>
              <div class="status-content">
                <h3 class="status-title">Génération automatique de votre CV</h3>
                <p class="status-description">
                  Notre IA crée votre profil professionnel à partir de vos informations
                </p>
              </div>
            </div>

            <div class="info-section">
              <div class="info-item">
                <mat-icon class="info-icon">psychology</mat-icon>
                <span>Analyse de vos informations d'inscription</span>
              </div>
              <div class="info-item">
                <mat-icon class="info-icon">description</mat-icon>
                <span>Génération automatique du CV</span>
              </div>
              <div class="info-item">
                <mat-icon class="info-icon">person</mat-icon>
                <span>Complétion du profil professionnel</span>
              </div>
              <div class="info-item">
                <mat-icon class="info-icon">notifications_active</mat-icon>
                <span>Notification dès que c'est prêt</span>
              </div>
            </div>
          </div>

          <!-- Progress Section -->
          <div class="progress-section">
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
            <p class="progress-text">Génération de votre CV en cours...</p>
          </div>

          <!-- Action Section -->
          <div class="action-section">
            <button
              class="primary-button"
              (click)="acknowledgeMessage()"
            >
              <span>Parfait, j'ai compris</span>
              <mat-icon>check</mat-icon>
            </button>

            <button
              class="secondary-button"
              (click)="exploreWhileWaiting()"
            >
              <mat-icon>explore</mat-icon>
              <span>Explorer en attendant</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- BOUTONS DE TEST TEMPORAIRES -->
    <div
      style="position: fixed; bottom: 10px; left: 10px; z-index: 10000; background: #fff; padding: 10px; border: 2px solid #001660; border-radius: 8px; font-size: 12px;"
    >
      <h4 style="margin: 0 0 10px 0; color: #001660;">🧪 Debug Message</h4>
      <button
        (click)="forceShowMessageForTesting()"
        style="margin: 2px; padding: 5px 8px; background: #001660; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px;"
      >
        Force Show
      </button>
      <button
        (click)="clearTestState()"
        style="margin: 2px; padding: 5px 8px; background: #ff6600; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px;"
      >
        Clear State
      </button>
      <button
        (click)="checkCurrentState()"
        style="margin: 2px; padding: 5px 8px; background: #666; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px;"
      >
        Check State
      </button>
      <div style="margin-top: 5px; font-size: 10px; color: #666;">
        showMessage: {{ showMessage }}
      </div>
    </div>
  `,
  styleUrls: ['./profile-completion-message.component.scss'],
  animations: [
    trigger('slideIn', [
      state('in', style({ transform: 'translateY(0)', opacity: 1 })),
      state('out', style({ transform: 'translateY(-100%)', opacity: 0 })),
      transition('out => in', [
        animate('0.3s ease-out')
      ]),
      transition('in => out', [
        animate('0.3s ease-in')
      ])
    ])
  ]
})
export class ProfileCompletionMessageComponent implements OnInit, OnDestroy {
  showMessage = false;
  private autoCloseTimer?: number;

  constructor(private signupStateService: SignupStateService) {}

  ngOnInit(): void {
    console.log('=== ProfileCompletionMessage - ngOnInit called ===');

    // Vérifier les valeurs dans localStorage pour debug détaillé
    const signupCompleted = localStorage.getItem('signup_completed');
    const messageShown = localStorage.getItem('profile_message_shown');

    console.log('=== DEBUG LOCALSTORAGE ===');
    console.log('signup_completed:', signupCompleted);
    console.log('profile_message_shown:', messageShown);
    console.log('localStorage keys:', Object.keys(localStorage));

    // Vérifier si le message doit être affiché
    const shouldShow = this.signupStateService.shouldShowProfileMessage();
    console.log('=== DECISION ===');
    console.log('shouldShowProfileMessage():', shouldShow);

    if (shouldShow) {
      console.log('✅ AFFICHAGE DU MESSAGE - Nouvel utilisateur détecté');
      this.showMessage = true;
    } else {
      console.log('❌ PAS D\'AFFICHAGE - Conditions non remplies');
      console.log('Raisons possibles:');
      console.log('- signup_completed n\'est pas "true":', signupCompleted !== 'true');
      console.log('- profile_message_shown est "true":', messageShown === 'true');
    }

    console.log('=== FIN DEBUG ngOnInit ===');
  }

  ngOnDestroy(): void {
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
    }
  }

  closeMessage(): void {
    console.log('closeMessage called - hiding message and marking as shown');
    // Fermer le message
    this.showMessage = false;
    // Marquer le message comme affiché pour qu'il ne se répète pas
    this.signupStateService.markProfileMessageShown();
    console.log('Message fermé - ne s\'affichera plus pour cet utilisateur');

    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
    }
  }

  acknowledgeMessage(): void {
    console.log('User acknowledged the message - closing message');
    // Fermer le message quand l'utilisateur clique "Parfait, j'ai compris"
    this.closeMessage();
  }

  exploreWhileWaiting(): void {
    console.log('User wants to explore while waiting - closing message');
    // Fermer le message quand l'utilisateur clique "Explorer en attendant"
    this.closeMessage();
  }

  // Méthodes de test pour diagnostiquer le problème
  forceShowMessageForTesting(): void {
    console.log('=== FORCE SHOW MESSAGE FOR TESTING ===');
    localStorage.setItem('signup_completed', 'true');
    localStorage.removeItem('profile_message_shown');
    console.log('Test state set - reloading component...');
    this.ngOnInit(); // Relancer la vérification
  }

  clearTestState(): void {
    console.log('=== CLEAR TEST STATE ===');
    localStorage.removeItem('signup_completed');
    localStorage.removeItem('profile_message_shown');
    this.showMessage = false;
    console.log('Test state cleared');
  }

  checkCurrentState(): void {
    console.log('=== CHECK CURRENT STATE ===');
    console.log('showMessage:', this.showMessage);
    console.log('localStorage signup_completed:', localStorage.getItem('signup_completed'));
    console.log('localStorage profile_message_shown:', localStorage.getItem('profile_message_shown'));
    console.log('shouldShowProfileMessage():', this.signupStateService.shouldShowProfileMessage());
  }
}
