import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { SignupStateService } from '../../services/signup-state.service';

@Component({
  selector: 'app-test-profile-message',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatIconModule],
  template: `
    <div class="test-container">
      <h3>🧪 Test du Message de Profil</h3>
      <div class="test-buttons">
        <button 
          mat-raised-button 
          color="primary" 
          (click)="forceShowMessage()"
        >
          <mat-icon>visibility</mat-icon>
          Forcer l'affichage du message
        </button>
        
        <button 
          mat-raised-button 
          color="warn" 
          (click)="resetState()"
        >
          <mat-icon>refresh</mat-icon>
          Reset l'état
        </button>
        
        <button 
          mat-raised-button 
          (click)="checkState()"
        >
          <mat-icon>info</mat-icon>
          Vérifier l'état
        </button>
      </div>
      
      <div class="instructions">
        <p><strong>Instructions de test :</strong></p>
        <ol>
          <li>Cliquez sur "Forcer l'affichage du message"</li>
          <li>Rechargez la page (F5)</li>
          <li>Le message devrait apparaître</li>
          <li>Utilisez "Reset l'état" pour nettoyer</li>
        </ol>
      </div>
    </div>
  `,
  styles: [`
    .test-container {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: white;
      border: 2px solid #001660;
      border-radius: 12px;
      padding: 1rem;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      max-width: 300px;
      z-index: 1000;
    }
    
    .test-container h3 {
      margin: 0 0 1rem 0;
      color: #001660;
      font-size: 1rem;
    }
    
    .test-buttons {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      margin-bottom: 1rem;
    }
    
    .test-buttons button {
      font-size: 0.8rem;
      padding: 0.5rem;
    }
    
    .instructions {
      font-size: 0.75rem;
      color: #666;
      border-top: 1px solid #eee;
      padding-top: 0.5rem;
    }
    
    .instructions ol {
      margin: 0.5rem 0 0 1rem;
      padding: 0;
    }
    
    .instructions li {
      margin-bottom: 0.25rem;
    }
  `]
})
export class TestProfileMessageComponent {
  constructor(private signupStateService: SignupStateService) {}

  forceShowMessage(): void {
    this.signupStateService.forceShowMessage();
    alert('État configuré ! Rechargez la page (F5) pour voir le message.');
  }

  resetState(): void {
    this.signupStateService.resetForTesting();
    alert('État nettoyé !');
  }

  checkState(): void {
    const signupCompleted = localStorage.getItem('signup_completed');
    const messageShown = localStorage.getItem('profile_message_shown');
    const shouldShow = this.signupStateService.shouldShowProfileMessage();
    
    console.log('=== État actuel ===');
    console.log('signup_completed:', signupCompleted);
    console.log('profile_message_shown:', messageShown);
    console.log('shouldShowProfileMessage():', shouldShow);
    
    alert(`État actuel:\n- signup_completed: ${signupCompleted}\n- message_shown: ${messageShown}\n- shouldShow: ${shouldShow}\n\nVoir console pour plus de détails.`);
  }
}
