<div class="signup-wrapper">
  <!-- Floating particles -->
  <div class="particle particle-1"></div>
  <div class="particle particle-2"></div>
  <div class="particle particle-3"></div>
  <div class="particle particle-4"></div>
  <div class="particle particle-5"></div>
  <div class="signup-container">
    <div class="signup-grid">
      <!-- Logo Section (Left) -->
      <div class="logo-section">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>

        <div class="logo-container">
          <div class="logo-badge">
            <span class="badge-text">Join Now</span>
          </div>
          <img src="/assets/images/logo.png" alt="Kairos IT Logo" class="logo-image" />
        </div>

        <h1 class="logo-text">Kairos IT</h1>
        <p class="logo-subtitle">Your Career Partner</p>

        <div class="platform-description">
          <p>Join our platform to connect with top employers and find your dream job.</p>
        </div>

        <div class="benefits-container">
          <h3 class="benefits-title">Why Sign Up Today?</h3>
          <div class="benefits-divider"></div>
        </div>

        <div class="feature-highlights">
          <div class="feature-item">
            <div class="feature-icon-container">
              <mat-icon class="feature-icon">work</mat-icon>
            </div>
            <div class="feature-content">
              <h3>Find Jobs</h3>
              <p>Access thousands of job listings from top companies.</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon-container">
              <mat-icon class="feature-icon">people</mat-icon>
            </div>
            <div class="feature-content">
              <h3>Network</h3>
              <p>Connect with professionals in your industry.</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon-container">
              <mat-icon class="feature-icon">trending_up</mat-icon>
            </div>
            <div class="feature-content">
              <h3>Grow</h3>
              <p>Develop your skills and advance your career.</p>
            </div>
          </div>
        </div>

        <div class="cta-container">
          <div class="cta-text">Ready to start your journey?</div>
          <div class="cta-arrow">
            <mat-icon>arrow_forward</mat-icon>
          </div>
        </div>
      </div>

      <!-- Form Section (Right) -->
      <div class="orange-accent-top"></div>
      <div class="form-section">
        <div class="title-container">
          <h2 class="form-title">Create Your Account <mat-icon class="title-icon">how_to_reg</mat-icon></h2>
          <p class="form-subtitle">Join our platform and discover amazing opportunities tailored for you</p>
        </div>
        <form [formGroup]="form" (ngSubmit)="signup()" class="signup-form">
          <div class="form-field-group">

            <!-- First Name and Last Name -->
            <div class="form-row">
              <div class="form-field">
                <div class="input-wrapper">
                  <mat-icon class="input-icon">person</mat-icon>
                  <input
                    formControlName="firstName"
                    type="text"
                    required
                    aria-label="First Name"
                    placeholder=" "
                  />
                  <label class="floating-label">First Name*</label>
                </div>
                <div class="error-message" *ngIf="hasError('firstName', 'required') && form.get('firstName')?.touched">
                  <mat-icon>error_outline</mat-icon> First Name is required
                </div>
              </div>
              <div class="form-field">
                <div class="input-wrapper">
                  <mat-icon class="input-icon">person_outline</mat-icon>
                  <input
                    formControlName="lastName"
                    type="text"
                    required
                    aria-label="Last Name"
                    placeholder=" "
                  />
                  <label class="floating-label">Last Name*</label>
                </div>
                <div class="error-message" *ngIf="hasError('lastName', 'required') && form.get('lastName')?.touched">
                  <mat-icon>error_outline</mat-icon> Last Name is required
                </div>
              </div>
            </div>

            <!-- Email -->
            <div class="form-field">
              <div class="input-wrapper">
                <mat-icon class="input-icon">email</mat-icon>
                <input
                  formControlName="email"
                  type="email"
                  (input)="onEmailInput($event)"
                  required
                  aria-label="Email"
                  placeholder=" "
                />
                <label class="floating-label">Email*</label>
              </div>
              <div class="error-message" *ngIf="hasError('email', 'required') && form.get('email')?.touched">
                <mat-icon>error_outline</mat-icon> Email is required
              </div>
              <div class="error-message" *ngIf="hasError('email', 'email') && form.get('email')?.touched">
                <mat-icon>error_outline</mat-icon> Invalid email format
              </div>
              <div class="error-message" *ngIf="hasError('email', 'emailExists') && form.get('email')?.touched">
                <mat-icon>error_outline</mat-icon> This email is already used.
                <button
                  mat-button
                  color="primary"
                  (click)="goToSignIn()"
                  style="text-transform: none;"
                  aria-label="Sign in with existing email"
                  class="accent-btn"
                >
                  Sign in
                </button>
              </div>
            </div>

            <!-- Phone Number -->
            <div class="form-field phone-field">
              <div class="input-wrapper">
                <mat-icon class="input-icon">phone</mat-icon>
                <span class="country-code" [matMenuTriggerFor]="phoneCodesMenu">
                  +{{ selectedPhoneCode?.number || '1' }}
                  <mat-icon class="dropdown-icon">arrow_drop_down</mat-icon>
                </span>
                <input
                  formControlName="phoneNumber"
                  type="tel"
                  required
                  aria-label="Phone Number"
                  [maxlength]="phoneMaxLength"
                  placeholder=" "
                />
                <label class="floating-label">Phone Number*</label>
              </div>
              <div class="error-message" *ngIf="hasError('phoneNumber', 'required') && form.get('phoneNumber')?.touched">
                <mat-icon>error_outline</mat-icon> Phone Number is required
              </div>
              <div class="error-message" *ngIf="hasError('phoneNumber', 'phoneNotValid') && form.get('phoneNumber')?.touched">
                <mat-icon>error_outline</mat-icon> Invalid phone number for the selected country
              </div>
            </div>

            <!-- Date of Birth -->
            <div class="form-field">
              <div class="input-wrapper">
                <mat-icon class="input-icon">calendar_today</mat-icon>
                <input
                  formControlName="birthday"
                  type="date"
                  required
                  aria-label="Date of Birth"
                  placeholder=" "
                />
                <label class="floating-label">Date of Birth (YYYY-MM-DD)*</label>
              </div>
              <div class="error-message" *ngIf="hasError('birthday', 'required') && form.get('birthday')?.touched">
                <mat-icon>error_outline</mat-icon> Date of Birth is required
              </div>
            </div>

            <!-- Address -->
            <div class="form-field">
              <div class="input-wrapper">
                <mat-icon class="input-icon">home</mat-icon>
                <input
                  formControlName="address"
                  type="text"
                  aria-label="Address"
                  placeholder=" "
                />
                <label class="floating-label">Address</label>
              </div>
            </div>
          </div>

          <div class="form-field-group">

            <!-- Password and Confirm Password -->
            <div class="form-row">
              <div class="form-field">
                <div class="input-wrapper">
                  <mat-icon class="input-icon">lock</mat-icon>
                  <input
                    formControlName="password"
                    [type]="showPassword ? 'text' : 'password'"
                    required
                    aria-label="Password"
                    placeholder=" "
                  />
                  <label class="floating-label">Password*</label>
                  <button
                    class="toggle-password"
                    type="button"
                    (click)="togglePasswordVisibility()"
                    aria-label="Toggle password visibility"
                  >
                    <mat-icon>{{ showPassword ? 'visibility' : 'visibility_off' }}</mat-icon>
                  </button>
                </div>
                <div class="error-message" *ngIf="hasError('password', 'required') && form.get('password')?.touched">
                  <mat-icon>error_outline</mat-icon> Password is required
                </div>
                <div class="error-message" *ngIf="hasError('password', 'minlength') && form.get('password')?.touched">
                  <mat-icon>error_outline</mat-icon> Password must be at least 8 characters
                </div>
              </div>
              <div class="form-field">
                <div class="input-wrapper">
                  <mat-icon class="input-icon">lock</mat-icon>
                  <input
                    formControlName="confirmPassword"
                    [type]="showConfirmPassword ? 'text' : 'password'"
                    required
                    aria-label="Confirm Password"
                    placeholder=" "
                  />
                  <label class="floating-label">Confirm Password*</label>
                  <button
                    class="toggle-password"
                    type="button"
                    (click)="toggleConfirmPasswordVisibility()"
                    aria-label="Toggle confirm password visibility"
                  >
                    <mat-icon>{{ showConfirmPassword ? 'visibility' : 'visibility_off' }}</mat-icon>
                  </button>
                </div>
                <div class="error-message" *ngIf="hasError('confirmPassword', 'required') && form.get('confirmPassword')?.touched">
                  <mat-icon>error_outline</mat-icon> Confirmation is required
                </div>
                <div
                  class="error-message"
                  *ngIf="form.hasError('passwordsDoNotMatch') && form.get('confirmPassword')?.touched"
                >
                  <mat-icon>error_outline</mat-icon> Passwords do not match
                </div>
              </div>
            </div>
          </div>

          <div class="form-field-group">

            <!-- Upload CV -->
            <div class="form-field">
              <div class="input-wrapper">
                <label for="cvUpload" class="upload-label">
                  <mat-icon class="input-icon">upload_file</mat-icon>
                  <span class="upload-text">{{ selectedFileName || 'Upload CV (PDF, DOC, DOCX)' }}</span>
                </label>
                <input
                  type="file"
                  id="cvUpload"
                  accept=".pdf,.doc,.docx"
                  (change)="onFileSelected($event)"
                  style="display: none;"
                  aria-label="Upload CV"
                />
              </div>
            </div>

            <!-- Agree to Terms -->
            <div class="form-field">
              <mat-checkbox formControlName="agreeToTerms" aria-label="Agree to Terms" class="terms-checkbox">
                I agree to the <a href="/terms" target="_blank" class="terms-link">Terms and Conditions</a>*
              </mat-checkbox>
              <div class="error-message" *ngIf="hasError('agreeToTerms', 'required') && form.get('agreeToTerms')?.touched">
                <mat-icon>error_outline</mat-icon> You must agree to the Terms and Conditions
              </div>
            </div>
          </div>

          <!-- Sign Up Button -->
          <button
            class="signup-btn"
            type="submit"
            [disabled]="isSubmitting"
            aria-label="Sign Up"
          >
            <span>{{ isSubmitting ? 'Signing Up...' : 'Sign Up' }}</span>
            <mat-icon>arrow_forward</mat-icon>
          </button>

          <!-- Already Registered Link -->
          <div class="signup-link">
            Already have an account?
            <a (click)="goToSignIn()" role="link" aria-label="Login">Sign In</a>
          </div>

          <!-- Social Login -->
          <div class="social-signup">
            <div class="social-divider">Or sign up with</div>
            <div class="social-buttons">
              <a
                href="http://localhost:8081/oauth2/authorization/google"
                class="social-btn google"
                aria-label="Sign up with Google"
              >
                <div class="social-btn-icon">
                  <img src="/assets/images/google.png" alt="Google" />
                </div>
                <span class="social-btn-text">Google</span>
              </a>
              <a
                href="http://localhost:8081/oauth2/authorization/github"
                class="social-btn github"
                aria-label="Sign up with GitHub"
              >
                <div class="social-btn-icon">
                  <img src="/assets/images/github.png" alt="GitHub" />
                </div>
                <span class="social-btn-text">GitHub</span>
              </a>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Phone Codes Menu -->
  <mat-menu #phoneCodesMenu="matMenu">
    <ng-container *ngFor="let p of phoneCodes">
      <ng-container *ngIf="isFavorite(p.code)">
        <button mat-menu-item (click)="selectPhoneCode(p)">
          {{ p.code }} (+{{ p.number }})
        </button>
      </ng-container>
    </ng-container>
    <mat-divider></mat-divider>
    <ng-container *ngFor="let p of phoneCodes">
      <ng-container *ngIf="!isFavorite(p.code)">
        <button mat-menu-item (click)="selectPhoneCode(p)">
          {{ p.code }} (+{{ p.number }})
        </button>
      </ng-container>
    </ng-container>
  </mat-menu>
</div>
