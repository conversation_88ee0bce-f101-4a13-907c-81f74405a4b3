<div class="signup-wrapper">
  <div class="signup-container">
    <div class="signup-grid">
      <!-- Logo Section (Left) -->
      <div class="logo-section">
        <div class="logo-container">
          <img src="/assets/images/logo.png" alt="Kairos IT Logo" class="logo-image" />
        </div>

        <h1 class="logo-text">Kairos IT</h1>
        <div class="logo-underline"></div>

        <div class="welcome-text">
          <span class="welcome-dot"></span>
          <p>Welcome to sign up to Kairos IT</p>
          <span class="welcome-dot"></span>
        </div>

        <div class="benefits-list">
          <div class="benefit-item">
            <div class="check-icon">
              <mat-icon>check_circle</mat-icon>
            </div>
            <p>Connect with top employers</p>
          </div>
          <div class="benefit-item">
            <div class="check-icon">
              <mat-icon>check_circle</mat-icon>
            </div>
            <p>Build your professional profile</p>
          </div>
          <div class="benefit-item">
            <div class="check-icon">
              <mat-icon>check_circle</mat-icon>
            </div>
            <p>Access exclusive job opportunities</p>
          </div>
          <div class="benefit-item">
            <div class="check-icon">
              <mat-icon>check_circle</mat-icon>
            </div>
            <p>Grow your career network</p>
          </div>
        </div>

        <div class="join-button-container">
          <button class="join-button">Join our community of professionals today!</button>
        </div>
      </div>

      <!-- Form Section (Right) -->
      <div class="form-section">
        <div class="title-container">
          <h2 class="form-title">Sign Up</h2>
          <div class="title-underline"></div>
        </div>

        <form [formGroup]="form" (ngSubmit)="signup()" class="signup-form">
          <!-- First Name and Last Name -->
          <div class="form-row">
            <div class="form-field">
              <div class="input-wrapper">
                <mat-icon class="input-icon">person</mat-icon>
                <input
                  formControlName="firstName"
                  type="text"
                  required
                  aria-label="First Name"
                  placeholder="First Name*"
                />
              </div>
              <div class="error-message" *ngIf="hasError('firstName', 'required') && form.get('firstName')?.touched">
                <mat-icon>error_outline</mat-icon> First Name is required
              </div>
            </div>
            <div class="form-field">
              <div class="input-wrapper">
                <mat-icon class="input-icon">person_outline</mat-icon>
                <input
                  formControlName="lastName"
                  type="text"
                  required
                  aria-label="Last Name"
                  placeholder="Last Name*"
                />
              </div>
              <div class="error-message" *ngIf="hasError('lastName', 'required') && form.get('lastName')?.touched">
                <mat-icon>error_outline</mat-icon> Last Name is required
              </div>
            </div>
          </div>

          <!-- Email -->
          <div class="form-field">
            <div class="input-wrapper">
              <mat-icon class="input-icon">email</mat-icon>
              <input
                formControlName="email"
                type="email"
                (input)="onEmailInput($event)"
                required
                aria-label="Email"
                placeholder="Email*"
              />
            </div>
            <div class="error-message" *ngIf="hasError('email', 'required') && form.get('email')?.touched">
              <mat-icon>error_outline</mat-icon> Email is required
            </div>
            <div class="error-message" *ngIf="hasError('email', 'email') && form.get('email')?.touched">
              <mat-icon>error_outline</mat-icon> Invalid email format
            </div>
            <div class="error-message" *ngIf="hasError('email', 'emailExists') && form.get('email')?.touched">
              <mat-icon>error_outline</mat-icon> This email is already used.
              <button
                mat-button
                color="primary"
                (click)="goToSignIn()"
                style="text-transform: none;"
                aria-label="Sign in with existing email"
              >
                Sign in
              </button>
            </div>
          </div>

          <!-- Phone Number -->
          <div class="form-field">
            <div class="input-wrapper">
              <mat-icon class="input-icon">phone</mat-icon>
              <input
                formControlName="phoneNumber"
                type="tel"
                required
                aria-label="Phone Number"
                placeholder="Phone Number*"
              />
            </div>
            <div class="error-message" *ngIf="hasError('phoneNumber', 'required') && form.get('phoneNumber')?.touched">
              <mat-icon>error_outline</mat-icon> Phone Number is required
            </div>
            <div class="error-message" *ngIf="hasError('phoneNumber', 'phoneNotValid') && form.get('phoneNumber')?.touched">
              <mat-icon>error_outline</mat-icon> Invalid phone number
            </div>
          </div>

          <!-- Date of Birth -->
          <div class="form-field">
            <div class="date-label">Date of Birth (YYYY-MM-DD)*</div>
            <div class="input-wrapper date-wrapper">
              <mat-icon class="input-icon">calendar_today</mat-icon>
              <input
                formControlName="birthday"
                type="date"
                required
                aria-label="Date of Birth"
                placeholder="jj/mm/aaaa"
              />
            </div>
            <div class="error-message" *ngIf="hasError('birthday', 'required') && form.get('birthday')?.touched">
              <mat-icon>error_outline</mat-icon> Date of Birth is required
            </div>
          </div>

          <!-- Address -->
          <div class="form-field">
            <div class="input-wrapper">
              <mat-icon class="input-icon">home</mat-icon>
              <input
                formControlName="address"
                type="text"
                aria-label="Address"
                placeholder="Address"
              />
            </div>
          </div>

          <!-- Password and Confirm Password -->
          <div class="form-row">
            <div class="form-field">
              <div class="input-wrapper">
                <mat-icon class="input-icon">lock</mat-icon>
                <input
                  formControlName="password"
                  [type]="showPassword ? 'text' : 'password'"
                  required
                  aria-label="Password"
                  placeholder="Password*"
                />
                <button
                  class="toggle-password"
                  type="button"
                  (click)="togglePasswordVisibility()"
                  aria-label="Toggle password visibility"
                >
                  <mat-icon>{{ showPassword ? 'visibility' : 'visibility_off' }}</mat-icon>
                </button>
              </div>
              <div class="error-message" *ngIf="hasError('password', 'required') && form.get('password')?.touched">
                <mat-icon>error_outline</mat-icon> Password is required
              </div>
              <div class="error-message" *ngIf="hasError('password', 'minlength') && form.get('password')?.touched">
                <mat-icon>error_outline</mat-icon> Password must be at least 8 characters
              </div>
            </div>
            <div class="form-field">
              <div class="input-wrapper">
                <mat-icon class="input-icon">lock</mat-icon>
                <input
                  formControlName="confirmPassword"
                  [type]="showConfirmPassword ? 'text' : 'password'"
                  required
                  aria-label="Confirm Password"
                  placeholder="Confirm Password*"
                />
                <button
                  class="toggle-password"
                  type="button"
                  (click)="toggleConfirmPasswordVisibility()"
                  aria-label="Toggle confirm password visibility"
                >
                  <mat-icon>{{ showConfirmPassword ? 'visibility' : 'visibility_off' }}</mat-icon>
                </button>
              </div>
              <div class="error-message" *ngIf="hasError('confirmPassword', 'required') && form.get('confirmPassword')?.touched">
                <mat-icon>error_outline</mat-icon> Confirmation is required
              </div>
              <div
                class="error-message"
                *ngIf="form.hasError('passwordsDoNotMatch') && form.get('confirmPassword')?.touched"
              >
                <mat-icon>error_outline</mat-icon> Passwords do not match
              </div>
            </div>
          </div>

          <!-- Upload CV -->
          <div class="form-field">
            <div class="cv-label">CV*</div>
            <div class="cv-wrapper" (click)="fileInput.click()">
              <div class="upload-text">{{ selectedFileName || 'Upload CV (PDF, DOC, DOCX)' }}</div>
              <input
                #fileInput
                type="file"
                accept=".pdf,.doc,.docx"
                (change)="onFileSelected($event)"
                style="display: none;"
                required
                aria-label="Upload CV"
              />
            </div>
            <div class="error-message" *ngIf="!selectedFile && formSubmitted">
              <mat-icon>error_outline</mat-icon> CV is required
            </div>
          </div>

          <!-- Agree to Terms -->
          <div class="form-field terms-field">
            <mat-checkbox formControlName="agreeToTerms" aria-label="Agree to Terms" class="terms-checkbox">
              I agree to the <a href="/terms" target="_blank" class="terms-link">Terms and Conditions</a>*
            </mat-checkbox>
            <div class="error-message" *ngIf="hasError('agreeToTerms', 'required') && form.get('agreeToTerms')?.touched">
              <mat-icon>error_outline</mat-icon> You must agree to the Terms and Conditions
            </div>
          </div>

          <!-- Sign Up Button -->
          <button
            class="signup-btn"
            type="submit"
            [disabled]="isSubmitting"
            aria-label="Sign Up"
          >
            <span>{{ isSubmitting ? 'SIGNING UP...' : 'SIGN UP' }}</span>
          </button>

          <!-- Already Registered Link -->
          <div class="signup-link">
            Already have an account? <a (click)="goToSignIn()" role="link" aria-label="Login" class="signin-link">Sign in</a>
          </div>

          <div class="or-divider">
            <span>Or sign in with</span>
          </div>

          <!-- Social Login -->
          <div class="social-signup">
            <div class="social-buttons">
              <a
                href="http://localhost:8081/oauth2/authorization/google"
                class="social-btn"
                aria-label="Sign up with Google"
              >
                <img src="/assets/images/google.png" alt="Google" class="social-icon" />
              </a>
              <a
                href="http://localhost:8081/oauth2/authorization/github"
                class="social-btn"
                aria-label="Sign up with GitHub"
              >
                <img src="/assets/images/github.png" alt="GitHub" class="social-icon" />
              </a>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
