/* === Variables === */
:root {
    --primary-color: #001660; /* Bleu foncé exact de l'image */
    --primary-light: #0020a0;
    --primary-dark: #001040;
    --accent-color: #ff6b00; /* Orange exact de l'image */
    --accent-light: #ff8c3a;
    --text-color: #2d3748;
    --text-light: #718096;
    --bg-color: #e6eeff; /* Fond bleu clair exact de l'image */
    --white: #ffffff;
    --border-color: #e2e8f0;
    --shadow-color: rgba(0, 22, 96, 0.15);
    --success-color: #4CAF50; /* Vert pour les icônes de check */
    --error-color: #e53e3e;
    --google-color: #DB4437;
    --github-color: #333333;
}

/* === Global Reset and Base Styles === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
    background-color: #f5f7fb;
    color: var(--text-color);
}

/* === Material Icons === */
.material-icons {
    font-family: 'Material Icons' !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--primary-color);
}

/* === Wrapper === */
.signup-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: #f5f7fb;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

/* === Container === */
.signup-container {
    max-width: 900px;
    width: 98%;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    z-index: 1;
    height: auto;
}

/* === Grid Layout === */
.signup-grid {
    display: grid;
    grid-template-columns: 40% 60%;
}

/* === Logo Section === */
.logo-section {
    background-color: var(--bg-color);
    padding: 2.5rem 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    text-align: center;
    position: relative;
    border-right: 1px solid rgba(0, 22, 96, 0.08);
}

/* Logo container */
.logo-container {
    margin-bottom: 1rem;
}

.logo-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: contain;
    background-color: var(--white);
    padding: 5px;
    box-shadow: 0 2px 8px rgba(0, 22, 96, 0.1);
    border: 3px solid rgba(255, 255, 255, 0.8);
}

.logo-text {
    color: var(--primary-color);
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    letter-spacing: 0.5px;
}

.logo-underline {
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
    margin: 0 auto 1.5rem;
    border-radius: 3px;
}

.welcome-text {
    color: var(--text-color);
    font-size: 0.9rem;
    margin-bottom: 2rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
}

.welcome-text p {
    margin: 0 0.5rem;
}

.welcome-dot {
    width: 4px;
    height: 4px;
    background-color: var(--accent-color);
    border-radius: 50%;
    display: inline-block;
}

/* Benefits list */
.benefits-list {
    width: 100%;
    text-align: left;
    margin-bottom: 2rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.check-icon {
    color: var(--success-color);
    margin-right: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.check-icon mat-icon {
    font-size: 18px;
    height: 18px;
    width: 18px;
}

.benefit-item p {
    font-size: 0.85rem;
    color: var(--text-color);
    font-weight: 500;
}

.join-button-container {
    margin-top: 2rem;
    width: 90%;
}

.join-button {
    background-color: var(--white);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 25px;
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.join-button:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* === Form Section === */
.form-section {
    background-color: var(--white);
    padding: 2rem 2.5rem;
}

.title-container {
    text-align: center;
    margin-bottom: 2rem;
}

.form-title {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.title-underline {
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
    margin: 0 auto;
    border-radius: 3px;
}

/* === Form Structure === */
.signup-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-field {
    margin-bottom: 0.5rem;
    position: relative;
}

.input-wrapper {
    position: relative;
    width: 100%;
}

.input-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    z-index: 1;
    font-size: 16px;
    height: 16px;
    width: 16px;
}

input {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background-color: #ffffff;
    color: var(--text-color);
}

input::placeholder {
    color: var(--text-light);
    font-weight: 400;
}

input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 22, 96, 0.1);
}

input:focus + .input-icon {
    color: var(--primary-color);
}

/* Date field special styling */
input[type="date"] {
    color: var(--text-light);
}

input[type="date"]:focus,
input[type="date"]:valid {
    color: var(--text-color);
}

/* CV upload */
.cv-wrapper {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f9f9f9;
    margin-bottom: 0.5rem;
}

.cv-wrapper:hover {
    border-color: var(--primary-color);
    background-color: #f5f5f5;
}

.upload-text {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Terms checkbox */
.terms-field {
    margin: 1rem 0;
}

.terms-checkbox {
    font-size: 0.85rem;
    color: var(--text-color);
}

.terms-link {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
}

.terms-link:hover {
    text-decoration: underline;
}

/* Password toggle */
.toggle-password {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-light);
    z-index: 2;
}

.toggle-password:hover {
    color: var(--primary-color);
}

/* Error messages */
.error-message {
    color: var(--error-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
}

.error-message mat-icon {
    font-size: 14px;
    margin-right: 4px;
}

/* Sign up button */
.signup-btn {
    width: 100%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 14px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 1rem 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.signup-btn:hover {
    background-color: var(--primary-light);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 22, 96, 0.3);
}

.signup-btn:disabled {
    background-color: var(--text-light);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Sign in link */
.signup-link {
    text-align: center;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: var(--text-light);
}

.signin-link {
    color: var(--accent-color);
    text-decoration: none;
    cursor: pointer;
    font-weight: 500;
}

.signin-link:hover {
    text-decoration: underline;
}

/* Or divider */
.or-divider {
    text-align: center;
    position: relative;
    margin: 1rem 0;
    color: var(--text-light);
    font-size: 0.85rem;
}

.or-divider::before,
.or-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 40%;
    height: 1px;
    background-color: var(--border-color);
}

.or-divider::before {
    left: 0;
}

.or-divider::after {
    right: 0;
}

.or-divider span {
    background-color: white;
    padding: 0 15px;
    position: relative;
    z-index: 1;
}

/* Social login */
.social-signup {
    text-align: center;
    margin-top: 1rem;
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--white);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-btn:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.social-icon {
    width: 20px;
    height: 20px;
}

/* === Angular Material Overrides === */
::ng-deep .mat-mdc-checkbox {
    .mdc-checkbox {
        margin-right: 8px;
    }
    
    .mdc-checkbox__background {
        border-color: var(--border-color);
    }
    
    &.mat-mdc-checkbox-checked {
        .mdc-checkbox__background {
            background-color: var(--accent-color) !important;
            border-color: var(--accent-color) !important;
        }
    }
}

::ng-deep .mat-icon {
    font-family: 'Material Icons' !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* === Responsive Styles === */
@media (max-width: 992px) {
    .signup-grid {
        grid-template-columns: 1fr;
    }
    
    .logo-section {
        padding: 2rem;
        border-right: none;
        border-bottom: 1px solid rgba(0, 22, 96, 0.08);
    }
}

@media (max-width: 768px) {
    .signup-wrapper {
        padding: 1rem;
    }

    .signup-container {
        border-radius: 12px;
        width: 100%;
    }

    .form-section {
        padding: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .form-title {
        font-size: 1.25rem;
    }

    .logo-section {
        padding: 1.5rem;
    }

    .logo-text {
        font-size: 1.5rem;
    }

    .join-button {
        font-size: 0.75rem;
        padding: 0.6rem 0.8rem;
    }
}

@media (max-width: 576px) {
    .signup-wrapper {
        padding: 0.5rem;
    }

    .form-section {
        padding: 1rem;
    }

    .logo-section {
        padding: 1rem;
    }

    input {
        padding: 10px 10px 10px 36px;
        font-size: 0.85rem;
    }

    .signup-btn {
        padding: 12px;
        font-size: 0.85rem;
    }
}
