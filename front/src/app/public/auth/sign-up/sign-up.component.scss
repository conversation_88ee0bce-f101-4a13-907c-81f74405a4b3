/* === Variables === */
:root {
    --primary-color: #4299e1; /* Bleu ciel */
    --primary-light: #63b3ed;
    --primary-dark: #3182ce;
    --accent-color: #ed8936; /* Orange */
    --accent-light: #f6ad55;
    --text-color: #2d3748;
    --text-light: #718096;
    --bg-color: #ebf8ff;
    --white: #052ca5;
    --border-color: #e2e8f0;
    --shadow-color: rgba(66, 153, 225, 0.15);
    --success-color: #38a169;
    --error-color: #e53e3e;
    --google-color: #DB4437;
    --github-color: #333333;
}

/* === Global Reset and Base Styles === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
}

/* === Material Icons === */
.material-icons {
    font-family: 'Material Icons' !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #4299e1;
}

/* === Animations === */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(66, 153, 225, 0.4); transform: scale(1); }
    50% { box-shadow: 0 0 0 10px rgba(66, 153, 225, 0); transform: scale(1.05); }
    100% { box-shadow: 0 0 0 0 rgba(66, 153, 225, 0); transform: scale(1); }
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes borderPulse {
    0% { border-color: var(--primary-light); }
    50% { border-color: var(--accent-color); }
    100% { border-color: var(--primary-light); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    50% { background-position: 0% 0; }
    100% { background-position: 200% 0; }
}

@keyframes rotate3d {
    0% { transform: perspective(1000px) rotateY(0deg); }
    100% { transform: perspective(1000px) rotateY(360deg); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* === Wrapper === */
.signup-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(-45deg, #ebf8ff, #e6fffa, #ebf8ff, #e6f7ff);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect fill="none" width="100" height="100"/><circle fill="rgba(66, 153, 225, 0.03)" cx="50" cy="50" r="1"/></svg>'), linear-gradient(-45deg, #ebf8ff, #e6fffa, #ebf8ff, #e6f7ff);
}

.signup-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><rect fill="none" width="20" height="20"/><circle fill="rgba(66, 153, 225, 0.05)" cx="10" cy="10" r="1"/></svg>');
    z-index: 0;
}

.signup-wrapper::after {
    content: '';
    position: absolute;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(237, 137, 54, 0.08) 0%, rgba(237, 137, 54, 0) 70%);
    border-radius: 50%;
    top: -250px;
    right: -250px;
    z-index: 0;
    animation: pulse 10s infinite alternate;
}

/* Floating particles */
.particle {
    position: absolute;
    width: 10px;
    height: 10px;
    background: rgba(66, 153, 225, 0.2);
    border-radius: 50%;
    z-index: 0;
    animation: float 8s infinite ease-in-out;
}

.particle:nth-child(1) {
    top: 20%;
    left: 10%;
    width: 15px;
    height: 15px;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    top: 60%;
    left: 20%;
    width: 12px;
    height: 12px;
    animation-delay: 1s;
}

.particle:nth-child(3) {
    top: 40%;
    right: 15%;
    width: 18px;
    height: 18px;
    animation-delay: 2s;
    background: rgba(237, 137, 54, 0.2);
}

.particle:nth-child(4) {
    bottom: 30%;
    right: 25%;
    width: 10px;
    height: 10px;
    animation-delay: 3s;
}

.particle:nth-child(5) {
    bottom: 10%;
    left: 30%;
    width: 14px;
    height: 14px;
    animation-delay: 4s;
    background: rgba(237, 137, 54, 0.15);
}

/* === Container === */
.signup-container {
    max-width: 1200px;
    width: 98%;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(237, 137, 54, 0.12), 0 5px 10px rgba(237, 137, 54, 0.05);
    overflow: hidden;
    animation: fadeIn 0.8s ease-out forwards;
    position: relative;
    z-index: 1;
    border: 3px solid #ed8936;
    height: auto;
}

.signup-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--accent-color);
}

/* === Grid Layout === */
.signup-grid {
    display: grid;
    grid-template-columns: 42% 58%;
}

/* === Logo Section === */
.logo-section {
    background: linear-gradient(135deg, #4299e1 0%, #63b3ed 100%);
    color: var(--white);
    padding: 1rem 0.5rem 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
    border-right: 3px solid #ed8936;
}

.logo-section .material-icons,
.logo-section mat-icon {
    color: #ed8936 !important;
}

.logo-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 60%);
    transform: rotate(30deg);
}

.logo-section::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: -10px;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 60%);
    border-radius: 50%;
}

/* Floating shapes in the background */
.logo-section .shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.logo-section .shape-1 {
    width: 60px;
    height: 60px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.logo-section .shape-2 {
    width: 40px;
    height: 40px;
    top: 60%;
    left: 15%;
    animation-delay: 1s;
}

.logo-section .shape-3 {
    width: 80px;
    height: 80px;
    top: 30%;
    right: 10%;
    animation-delay: 2s;
}

/* Logo container and badge */
.logo-container {
    position: relative;
    margin-bottom: 1.5rem;
    display: inline-block;
}

.logo-badge {
    position: absolute;
    top: -15px;
    right: -15px;
    background: var(--accent-color);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    box-shadow: 0 4px 10px rgba(237, 137, 54, 0.4);
    transform: rotate(10deg);
    z-index: 3;
    animation: pulse 2s infinite;
}

.badge-text {
    position: relative;
    display: inline-block;
}

.badge-text::after {
    content: '!';
    position: absolute;
    right: -5px;
    animation: bounce 1s infinite;
}

.logo-image {
    width: 320px;
    filter: drop-shadow(0 8px 15px rgba(0,0,0,0.3));
    transition: all 0.5s ease;
    z-index: 2;
    position: relative;
    border-radius: 10px;
    border: 2px solid var(--accent-color);
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    margin-bottom: 0.75rem;
}

.logo-image::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 70%;
    height: 10px;
    background: radial-gradient(ellipse at center, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0) 70%);
    border-radius: 50%;
}

.logo-image:hover {
    transform: scale(1.08) rotate(2deg);
    filter: drop-shadow(0 15px 30px rgba(0,0,0,0.5));
    animation-play-state: paused;
    border-color: var(--accent-color);
}

.logo-text {
    font-size: 1.8rem;
    line-height: 1.3;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    font-weight: 800;
    margin-bottom: 0.3rem;
    position: relative;
    z-index: 2;
    background: linear-gradient(90deg, rgba(255,255,255,0.95), rgba(255,255,255,0.8), rgba(255,255,255,0.95));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    background-size: 200% auto;
    animation: shimmer 3s linear infinite;
    letter-spacing: 1px;
}

.logo-text::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: var(--accent-color);
    border-radius: 3px;
}

.logo-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-top: 0.3rem;
    font-weight: 600;
    animation: slideUp 1s ease-in-out 0.5s both;
    position: relative;
    z-index: 2;
    color: rgba(255, 255, 255, 0.95);
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    margin-bottom: 0.75rem;
}

/* Platform description */
.platform-description {
    margin: 0.5rem 0;
    padding: 0 1rem;
    animation: fadeIn 1.5s ease-in-out 1.5s both;
    position: relative;
    z-index: 2;
}

.platform-description p {
    font-size: 0.8rem;
    line-height: 1.3;
    color: rgba(255, 255, 255, 0.85);
    text-align: center;
    font-weight: 400;
    max-width: 90%;
    margin: 0 auto;
}

/* Benefits section */
.benefits-container {
    text-align: center;
    margin: 1rem 0;
    position: relative;
    animation: fadeIn 1s ease-in-out 0.8s both;
}

.benefits-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.3rem;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    position: relative;
    display: inline-block;
}

.benefits-title::before,
.benefits-title::after {
    content: '★';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: var(--accent-color);
    font-size: 0.8rem;
}

.benefits-title::before {
    left: -15px;
}

.benefits-title::after {
    right: -15px;
}

.benefits-divider {
    width: 50px;
    height: 2px;
    background: var(--accent-color);
    margin: 0.3rem auto;
    border-radius: 2px;
}

/* Feature highlights */
.feature-highlights {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
    max-width: 90%;
    margin: 0.75rem auto 1rem;
    animation: slideUp 1s ease-in-out 1s both;
    position: relative;
    z-index: 2;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--accent-color);
    opacity: 0.7;
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateX(5px) translateY(-2px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.25);
}

.feature-item:hover::before {
    width: 6px;
    opacity: 1;
}

.feature-icon-container {
    width: 50px;
    height: 50px;
    background: rgba(237, 137, 54, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    flex-shrink: 0;
}

.feature-icon-container::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px dashed #ed8936;
    animation: rotate 10s linear infinite;
}

.feature-icon {
    color: #ed8936;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon-container {
    transform: scale(1.1) rotate(10deg);
    background: rgba(237, 137, 54, 0.3);
}

.feature-item:hover .feature-icon {
    transform: scale(1.1);
}

.feature-content {
    flex: 1;
}

.feature-content h3 {
    font-size: 0.95rem;
    font-weight: 700;
    color: var(--white);
    margin: 0 0 0.15rem;
    letter-spacing: 0.3px;
}

.feature-content p {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    line-height: 1.3;
}

/* CTA section */
.cta-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin: 1rem 0;
    animation: fadeIn 1s ease-in-out 1.5s both;
}

.cta-text {
    font-size: 1rem;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.cta-arrow {
    width: 30px;
    height: 30px;
    background: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(237, 137, 54, 0.3);
}

.cta-arrow mat-icon {
    color: white;
    font-size: 1.2rem;
}

/* === Form Section === */
.orange-accent-top {
    position: absolute;
    top: 0;
    right: 0;
    width: 55%;
    height: 5px;
    background: var(--accent-color);
    z-index: 2;
}

.form-section {
    padding: 0 1.5rem 1.5rem;
    background: #ffffff;
    position: relative;
    overflow: hidden;
    border-left: 4px solid #ed8936;
    box-shadow: inset 0 0 30px rgba(66, 153, 225, 0.05), 0 10px 30px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}

.form-section .material-icons,
.form-section mat-icon {
    color: #4299e1 !important;
}

.form-section::before {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(237, 137, 54, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
    border-radius: 50%;
    z-index: 0;
    animation: pulse 8s infinite alternate;
}

.form-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(66, 153, 225, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
    border-radius: 50%;
    z-index: 0;
    animation: pulse 6s infinite alternate-reverse;
}

.signup-form {
    width: 100%;
    position: relative;
    z-index: 2;
    max-width: 650px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    padding: 0.75rem 1.5rem 1.25rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 2px solid rgba(237, 137, 54, 0.2);
    animation: fadeIn 0.8s ease-out, slideUp 0.6s ease-out;
}

/* Title container and elements */
.title-container {
    text-align: left;
    margin-bottom: 0.75rem;
    position: relative;
    animation: fadeIn 0.8s;
    padding-bottom: 0.25rem;
    margin-top: 0;
    width: 100%;
    padding-left: 0;
    border-bottom: 2px solid rgba(237, 137, 54, 0.15);
    position: sticky;
    top: 0;
    background: white;
    z-index: 10;
}

.form-title {
    font-size: 1.6rem;
    margin-bottom: 0.25rem;
    color: #4299e1;
    font-weight: 800;
    position: relative;
    letter-spacing: -0.5px;
    text-align: left;
    display: inline-block;
    border-bottom: 3px solid #ed8936;
    padding-bottom: 3px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.05);
    margin-top: 0;
    padding-left: 0;
}

.title-icon {
    font-size: 24px;
    color: #4299e1;
    vertical-align: middle;
    margin-left: 8px;
}

.form-subtitle {
    font-size: 0.85rem;
    color: #718096;
    max-width: 95%;
    margin: 0.25rem 0 0.5rem;
    line-height: 1.3;
    animation: fadeIn 1s 0.5s both;
    position: relative;
    display: block;
    clear: both;
    text-align: left;
    padding-left: 0;
}

@keyframes widthPulse {
    0% { width: 50px; }
    50% { width: 100px; }
    100% { width: 50px; }
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* === Form Fields === */
.form-field {
    margin-bottom: 1rem;
    animation: slideUp 0.6s 0.3s both;
    position: relative;
    padding-top: 0.15rem;
    transition: all 0.3s ease;
}

.form-field:hover .input-wrapper {
    border-color: rgba(66, 153, 225, 0.4);
}

.form-field-group {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.25rem 1.5rem;
    margin-bottom: 1.25rem;
    border-left: 3px solid #ed8936;
    position: relative;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    margin-top: 0.5rem;
    width: 100%;
}

.form-field-group:hover {
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
    border-left-width: 5px;
}

.form-row {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 0.5rem;
}

.form-row .form-field {
    flex: 1;
}

.input-wrapper {
    position: relative;
    width: 100%;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.03);
    transition: all 0.3s ease;
    overflow: visible;
    background: #ffffff;
    margin-top: 15px;
    height: 55px;
    z-index: 1;
}

.input-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    background: linear-gradient(to right, transparent, rgba(66, 153, 225, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    background-size: 200% 100%;
}

.input-wrapper:focus-within {
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15), 0 8px 15px rgba(0,0,0,0.05);
    border-bottom: 3px solid #ed8936;
    transform: translateY(-2px);
    z-index: 10;
}

.phone-field .input-wrapper:focus-within,
.cv-field .input-wrapper:focus-within {
    z-index: 20;
}

.cv-field input {
    padding-left: 55px;
    z-index: 5;
}

.cv-field .input-wrapper {
    z-index: 5;
}

.input-wrapper:focus-within::before {
    opacity: 1;
    animation: shimmer 2s infinite;
}

.input-wrapper:hover {
    border-color: rgba(66, 153, 225, 0.5);
    box-shadow: 0 5px 12px rgba(66, 153, 225, 0.1);
    transform: translateY(-1px);
}

.input-wrapper:focus-within .input-icon {
    color: #4299e1;
    background: rgba(66, 153, 225, 0.2);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 3px 8px rgba(66, 153, 225, 0.2);
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #4299e1;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(66, 153, 225, 0.08);
    border-radius: 50%;
    padding: 5px;
    z-index: 3;
    pointer-events: none;
}

.phone-field .input-icon {
    z-index: 5;
}

.cv-field .input-icon {
    z-index: 4;
    left: 12px;
}

input {
    width: 100%;
    padding: 15px 15px 15px 55px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: transparent;
    color: #2d3748;
    font-weight: 500;
    letter-spacing: 0.3px;
    height: 100%;
    z-index: 2;
    position: relative;
}

input:focus {
    outline: none;
}

.floating-label {
    position: absolute;
    left: 55px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1rem;
    color: #718096;
    pointer-events: none;
    transition: all 0.3s ease;
    background: transparent;
    font-weight: 500;
    z-index: 3;
    white-space: nowrap;
}

input:focus + .floating-label,
input:not(:placeholder-shown) + .floating-label {
    top: -12px;
    left: 15px;
    font-size: 0.8rem;
    padding: 3px 10px;
    color: #4299e1;
    font-weight: 600;
    letter-spacing: 0.4px;
    z-index: 15;
    transform: translateY(0);
    animation: labelFloat 0.3s ease forwards;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(66, 153, 225, 0.2);
    border-bottom: 3px solid #ed8936;
    background: white;
    pointer-events: none;
    max-width: 90%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@keyframes labelFloat {
    0% { transform: translateY(10px); opacity: 0.8; }
    50% { transform: translateY(-2px); opacity: 0.9; }
    100% { transform: translateY(0); opacity: 1; }
}

.toggle-password {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(66, 153, 225, 0.1);
    border: none;
    cursor: pointer;
    color: #4299e1;
    transition: all 0.3s ease;
    padding: 8px;
    z-index: 10;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.toggle-password:hover {
    color: #ed8936;
    background: rgba(66, 153, 225, 0.15);
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 3px 10px rgba(66, 153, 225, 0.2);
}

.toggle-password:active {
    transform: translateY(-50%) scale(0.95);
}

/* Phone field specific styles */
.phone-field .input-wrapper {
    padding-left: 80px; /* Make room for country code */
}

.phone-field input {
    padding-left: 35px;
}

.phone-field .floating-label {
    left: 90px;
    z-index: 4;
}

.phone-field input:focus + .floating-label,
.phone-field input:not(:placeholder-shown) + .floating-label {
    left: 15px;
}

.country-code {
    position: absolute;
    left: 45px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #2d3748;
    cursor: pointer;
    z-index: 4;
    transition: all 0.3s ease;
}

.country-code:hover {
    color: var(--primary-color);
}

.dropdown-icon {
    font-size: 1rem;
    margin-left: 4px;
    color: var(--primary-color)!important;
    transition: all 0.3s ease;
}

.country-code:hover .dropdown-icon {
    transform: translateY(2px);
}

/* Upload label */
.upload-label {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 14px;
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(66, 153, 225, 0.03);
    position: relative;
    overflow: hidden;
}

/* CV field specific styles */
.cv-field .floating-label {
    left: 55px;
    z-index: 6;
    background: rgba(255, 255, 255, 0.9);
    padding: 2px 5px;
    border-radius: 4px;
}

.cv-field input:focus + .floating-label,
.cv-field input:not(:placeholder-shown) + .floating-label {
    left: 15px;
    z-index: 20;
    background: white;
}

.upload-label::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(66, 153, 225, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.upload-label:hover {
    border-color: var(--primary-color);
    background: rgba(66, 153, 225, 0.07);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.1);
}

.upload-label:hover::before {
    transform: translateX(100%);
}

.upload-text {
    color: var(--text-light);
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.upload-label:hover .upload-text {
    color: var(--primary-color);
}

.upload-label:hover .input-icon {
    color: var(--accent-color);
    transform: scale(1.1);
}

/* Error messages */
.error-message {
    color: var(--error-color);
    font-size: 0.85rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-left: 3px solid var(--error-color);
    background: rgba(229, 62, 62, 0.05);
    border-radius: 0 6px 6px 0;
    animation: slideInLeft 0.3s ease-out;
    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.1);
}

.error-message::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 5px;
    height: 5px;
    background: var(--accent-color);
    border-radius: 50%;
}

.error-message mat-icon {
    margin-right: 8px;
    font-size: 18px;
    height: 18px;
    width: 18px;
}

/* === Terms checkbox === */
.terms-checkbox {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    padding: 1rem;
    background: rgba(237, 137, 54, 0.05);
    border-radius: 10px;
    border-left: 3px solid var(--accent-color);
    transition: all 0.3s ease;
}

.terms-checkbox:hover {
    background: rgba(237, 137, 54, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(237, 137, 54, 0.08);
}

.terms-link {
    color: var(--accent-color);
    font-weight: 600;
    text-decoration: none;
    position: relative;
    transition: all 0.3s ease;
    margin-left: 5px;
    margin-right: 5px;
}

.terms-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: all 0.3s ease;
}

.terms-link:hover {
    color: var(--accent-color);
}

.terms-link:hover::after {
    width: 100%;
}

/* === Buttons === */
.signup-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #4299e1 0%, #63b3ed 100%)!important;
    color: var(--white);
    border: none;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 700;
    letter-spacing: 0.5px;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(66, 153, 225, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    animation: slideUp 0.6s 0.5s both;
    border: 1px solid var(--accent-color);
    margin: 1rem 0;
    position: relative;
    z-index: 1;
}

.signup-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, #3182ce, transparent);
    transition: all 0.6s ease;
    z-index: -1;
}

.signup-btn mat-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2rem;
    opacity: 0;
    transition: all 0.3s ease;
}

.signup-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(66, 153, 225, 0.3);
    padding-right: 35px;
    background-image: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    border-color: var(--accent-color);
}

.signup-btn:hover::before {
    left: 100%;
}

.signup-btn:hover mat-icon {
    opacity: 1;
    right: 25px;
}

.signup-btn:active {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(66, 153, 225, 0.3);
}

.signup-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.3), 0 10px 25px rgba(66, 153, 225, 0.3);
}

.signup-btn:disabled {
    background: #0f38b8;
    color: #999999;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Accent button (for terms link) */
.accent-btn {
    background: transparent;
    color: var(--primary-color);
    border: none;
    padding: 4px 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border-radius: 4px;
}

.accent-btn::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: all 0.3s ease;
}

.accent-btn:hover {
    color: var(--accent-color);
    background: rgba(66, 153, 225, 0.05);
}

.accent-btn:hover::after {
    width: 100%;
}

/* === Social Login === */
.social-signup {
    margin-top: 2rem;
    text-align: center;
    position: relative;
    animation: slideUp 0.6s 0.7s both;
}

.social-divider {
    display: inline-block;
    position: relative;
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 1.5rem;
    font-weight: 600;
    padding: 0 15px;
}

.social-divider::before,
.social-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 80px;
    height: 1px;
    background: var(--border-color);
}

.social-divider::before {
    right: 100%;
    margin-right: 10px;
}

.social-divider::after {
    left: 100%;
    margin-left: 10px;
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
}

.social-btn {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    border-radius: 50px;
    background: var(--white);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 2px solid var(--border-color);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    color: var(--text-color);
    font-weight: 600;
    min-width: 180px;
}

.social-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: all 0.6s ease;
}

.social-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.social-btn:hover::before {
    left: 100%;
}

.social-btn-icon {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    transition: all 0.3s ease;
}

.social-btn img {
    width: 20px;
    height: 20px;
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.social-btn:hover img {
    transform: scale(1.2) rotate(10deg);
}

.social-btn:active {
    transform: translateY(-2px);
}

.social-btn-text {
    font-size: 1rem;
    transition: all 0.3s ease;
}

/* Google button */
.social-btn.google {
    border-color: rgba(219, 68, 55, 0.3);
    color: #DB4437;
}

.social-btn.google:hover {
    background: rgba(219, 68, 55, 0.05);
    border-color: rgba(219, 68, 55, 0.5);
}

/* GitHub button */
.social-btn.github {
    border-color: rgba(51, 51, 51, 0.3);
    color: #333333;
}

.social-btn.github:hover {
    background: rgba(51, 51, 51, 0.05);
    border-color: rgba(51, 51, 51, 0.5);
}

/* === Sign In Link === */
.signup-link {
    text-align: center;
    margin-top: 1.5rem;
    font-size: 1rem;
    color: var(--text-light);
    animation: slideUp 0.6s 0.8s both;
    padding: 1rem;
    background: rgba(237, 137, 54, 0.05);
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(237, 137, 54, 0.08);
    transition: all 0.3s ease;
    border-top: 2px solid var(--accent-color);
}

.signup-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--accent-color);
    border-radius: 4px 0 0 4px;
}

.signup-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(66, 153, 225, 0.12);
}

.signup-link a {
    color: var(--accent-color);
    font-weight: 700;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    padding-bottom: 2px;
    margin-left: 5px;
    cursor: pointer;
}

.signup-link a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: all 0.3s ease;
}

.signup-link a:hover {
    color: var(--accent-color);
}

.signup-link a:hover::after {
    width: 100%;
}

/* === Responsive Design === */
@media (max-width: 1200px) {
    .signup-container {
        width: 95%;
    }
}

@media (max-width: 992px) {
    .signup-grid {
        grid-template-columns: 1fr;
    }

    .logo-section {
        padding: 3rem;
    }

    .form-section {
        padding: 3rem;
    }

    .logo-section .shape-1,
    .logo-section .shape-2,
    .logo-section .shape-3 {
        display: none;
    }

    .logo-image {
        width: 180px;
        margin-bottom: 1.5rem;
    }

    .logo-text {
        font-size: 1.4rem;
        margin-bottom: 1.5rem;
    }

    .logo-subtitle {
        margin-bottom: 1rem;
    }

    .form-row {
        flex-direction: column;
        gap: 1.25rem;
    }

    .social-buttons {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .signup-wrapper {
        padding: 1.5rem;
    }

    .signup-container {
        width: 100%;
        border-radius: 16px;
    }

    .logo-section {
        padding: 2.5rem 2rem;
    }

    .form-section {
        padding: 2.5rem 2rem;
    }

    .form-title {
        font-size: 2rem;
    }

    .particle {
        display: none;
    }

    .social-icon {
        width: 50px;
        height: 50px;
    }

    .social-icon img {
        width: 24px;
        height: 24px;
    }
}

@media (max-width: 576px) {
    .signup-wrapper {
        padding: 1rem;
    }

    .signup-container {
        border-radius: 12px;
    }

    .logo-section {
        padding: 2rem 1.5rem;
    }

    .form-section {
        padding: 2rem 1.5rem;
    }

    .form-title {
        font-size: 1.75rem;
    }

    .input-wrapper {
        border-radius: 10px;
    }

    input {
        padding: 16px 16px 16px 50px;
        font-size: 1rem;
    }

    .input-icon {
        left: 16px;
        font-size: 1.25rem;
    }

    .signup-btn {
        padding: 16px;
        font-size: 1rem;
    }

    .social-divider::before,
    .social-divider::after {
        width: 60px;
    }

    .signup-link {
        padding: 1rem;
        font-size: 0.9rem;
    }

    .error-message {
        font-size: 0.8rem;
        padding: 6px 10px;
    }
}
