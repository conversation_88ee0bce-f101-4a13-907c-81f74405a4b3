/* === Variables === */
:root {
    --primary-color: #001660; /* Bleu foncé exact de l'image */
    --primary-light: #0020a0;
    --primary-dark: #001040;
    --accent-color: #001040; /* Couleur #001040FF pour les accents */
    --accent-light: #002060;
    --accent-gradient: linear-gradient(135deg, #001040 0%, #002060 100%);
    --orange-color: #ff6b00; /* Orange pour les touches élégantes */
    --orange-light: #ff8c3a;
    --orange-gradient: linear-gradient(135deg, #ff6b00 0%, #ff8c3a 100%);
    --text-color: #2d3748;
    --text-light: #718096;
    --bg-color: linear-gradient(135deg, #f8faff 0%, #eef4ff 50%, #e8f0ff 100%); /* Nouveau fond dégradé */
    --white: #040140;
    --border-color: #e2e8f0;
    --shadow-color: rgba(0, 16, 64, 0.15);
    --success-color: #4CAF50; /* Vert pour les icônes de check */
    --error-color: #e53e3e;
    --google-color: #DB4437;
    --github-color: #333333;
    --focus-color: #001040; /* Couleur de focus pour les inputs */
    --focus-shadow: rgba(0, 16, 64, 0.2);
}

/* === Global Reset and Base Styles === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
    background-color: #f5f7fb;
    color: var(--text-color);
}

/* === Material Icons === */
.material-icons {
    font-family: 'Material Icons' !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--primary-color);
}

/* === Wrapper === */
.signup-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: #f5f7fb;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

/* === Container === */
.signup-container {
    max-width: 900px;
    width: 98%;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    z-index: 1;
    height: auto;
}

/* === Grid Layout === */
.signup-grid {
    display: grid;
    grid-template-columns: 40% 60%;
}

/* === Logo Section === */
.logo-section {
    background: var(--bg-color);
    padding: 2.5rem 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    text-align: center;
    position: relative;
    border-right: 1px solid rgba(0, 22, 96, 0.08);
    overflow: hidden;
}

/* Effets de fond animés */
.logo-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at 30% 70%, rgba(255, 107, 0, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(0, 16, 64, 0.03) 0%, transparent 50%);
    animation: backgroundFloat 8s ease-in-out infinite;
    z-index: 0;
}

.logo-section::after {
    content: '';
    position: absolute;
    top: 10%;
    right: 10%;
    width: 100px;
    height: 100px;
    background: var(--orange-gradient);
    border-radius: 50%;
    opacity: 0.08;
    animation: floatingOrb 6s ease-in-out infinite;
    z-index: 0;
}

@keyframes backgroundFloat {
    0%, 100% {
        transform: translate(0, 0) rotate(0deg);
    }
    33% {
        transform: translate(10px, -10px) rotate(120deg);
    }
    66% {
        transform: translate(-10px, 10px) rotate(240deg);
    }
}

@keyframes floatingOrb {
    0%, 100% {
        transform: translateY(0) scale(1);
        opacity: 0.08;
    }
    50% {
        transform: translateY(-20px) scale(1.1);
        opacity: 0.12;
    }
}

.logo-section > * {
    position: relative;
    z-index: 1;
}

/* Logo container */
.logo-container {
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.logo-container::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: var(--orange-gradient);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.4s ease;
    z-index: -1;
    animation: logoHalo 4s ease-in-out infinite;
}

.logo-container:hover::before {
    opacity: 0.2;
    transform: scale(1.1);
}

@keyframes logoHalo {
    0%, 100% {
        opacity: 0;
        transform: scale(1);
    }
    50% {
        opacity: 0.1;
        transform: scale(1.05);
    }
}

.logo-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: contain;
    background-color: var(--white);
    padding: 5px;
    box-shadow:
        0 4px 15px rgba(0, 22, 96, 0.15),
        0 0 0 2px rgba(255, 107, 0, 0.1),
        inset 0 2px 4px rgba(255, 255, 255, 0.8);
    border: 3px solid rgba(255, 255, 255, 0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.logo-image:hover {
    transform: scale(1.05) rotate(5deg);
    box-shadow:
        0 8px 25px rgba(0, 22, 96, 0.2),
        0 0 0 3px rgba(255, 107, 0, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 107, 0, 0.2);
}

.logo-text {
    color: var(--primary-color);
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    letter-spacing: 0.5px;
}

.logo-underline {
    width: 50px;
    height: 3px;
    background: var(--orange-gradient);
    margin: 0 auto 1.5rem;
    border-radius: 3px;
    position: relative;
    overflow: hidden;
    animation: orangeGlow 3s ease-in-out infinite;
    box-shadow: 0 0 15px rgba(255, 107, 0, 0.4);
}

.logo-underline::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: orangeShimmer 2.5s ease-in-out infinite;
}

.logo-underline::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--orange-gradient);
    border-radius: 5px;
    opacity: 0;
    animation: orangePulse 3s ease-in-out infinite;
    z-index: -1;
}

@keyframes orangeGlow {
    0%, 100% {
        box-shadow: 0 0 15px rgba(255, 107, 0, 0.4);
        transform: scale(1);
    }
    50% {
        box-shadow:
            0 0 25px rgba(255, 107, 0, 0.6),
            0 0 35px rgba(255, 107, 0, 0.4),
            0 0 45px rgba(255, 107, 0, 0.2);
        transform: scale(1.05);
    }
}

@keyframes orangeShimmer {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

@keyframes orangePulse {
    0%, 100% {
        opacity: 0;
        transform: scale(1);
    }
    50% {
        opacity: 0.3;
        transform: scale(1.2);
    }
}

.welcome-text {
    color: var(--text-color);
    font-size: 0.9rem;
    margin-bottom: 2rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
}

.welcome-text p {
    margin: 0 0.5rem;
}

.welcome-dot {
    width: 4px;
    height: 4px;
    background: var(--orange-gradient);
    border-radius: 50%;
    display: inline-block;
    position: relative;
    animation: orangeDotPulse 2.5s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(255, 107, 0, 0.5);
}

.welcome-dot::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: var(--orange-gradient);
    border-radius: 50%;
    opacity: 0;
    animation: orangeRipple 2.5s ease-in-out infinite;
}

.welcome-dot::after {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    background: radial-gradient(circle, rgba(255, 107, 0, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    animation: orangeHalo 2.5s ease-in-out infinite 0.5s;
}

@keyframes orangeDotPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 10px rgba(255, 107, 0, 0.5);
    }
    50% {
        transform: scale(1.4);
        box-shadow:
            0 0 20px rgba(255, 107, 0, 0.8),
            0 0 30px rgba(255, 107, 0, 0.6),
            0 0 40px rgba(255, 107, 0, 0.4);
    }
}

@keyframes orangeRipple {
    0% {
        transform: scale(1);
        opacity: 0.6;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes orangeHalo {
    0% {
        transform: scale(1);
        opacity: 0;
    }
    50% {
        transform: scale(2);
        opacity: 0.4;
    }
    100% {
        transform: scale(3);
        opacity: 0;
    }
}

/* Benefits list */
.benefits-list {
    width: 100%;
    text-align: left;
    margin-bottom: 2rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.check-icon {
    color: var(--success-color);
    margin-right: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.check-icon mat-icon {
    font-size: 18px;
    height: 18px;
    width: 18px;
}

.benefit-item p {
    font-size: 0.85rem;
    color: var(--text-color);
    font-weight: 500;
}

.join-button-container {
    margin-top: 2rem;
    width: 90%;
}

.join-button {
    background: linear-gradient(135deg, var(--white) 0%, #fafbff 100%);
    color: var(--primary-color);
    border: 2px solid transparent;
    background-clip: padding-box;
    border-radius: 25px;
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    width: 100%;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 2px 8px rgba(0, 22, 96, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.join-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--orange-gradient);
    border-radius: 23px;
    opacity: 0;
    transition: all 0.4s ease;
    z-index: -1;
}

.join-button::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--orange-gradient);
    border-radius: 27px;
    z-index: -2;
    opacity: 0;
    transition: all 0.4s ease;
}

.join-button:hover {
    color: var(--white);
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px rgba(255, 107, 0, 0.3),
        0 4px 15px rgba(0, 22, 96, 0.1);
}

.join-button:hover::before {
    opacity: 1;
}

.join-button:hover::after {
    opacity: 1;
    animation: buttonGlow 0.6s ease-out;
}

@keyframes buttonGlow {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* === Form Section === */
.form-section {
    background-color: var(--white);
    padding: 2rem 2.5rem;
}

.title-container {
    text-align: center;
    margin-bottom: 2rem;
}

.form-title {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.title-underline {
    width: 50px;
    height: 3px;
    background: var(--orange-gradient);
    margin: 0 auto;
    border-radius: 3px;
    position: relative;
    overflow: hidden;
    animation: titleOrangeGlow 3s ease-in-out infinite;
    box-shadow: 0 0 15px rgba(255, 107, 0, 0.4);
}

.title-underline::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: titleOrangeShimmer 2.5s ease-in-out infinite;
}

.title-underline::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--orange-gradient);
    border-radius: 5px;
    opacity: 0;
    animation: titleOrangePulse 3s ease-in-out infinite;
    z-index: -1;
}

@keyframes titleOrangeGlow {
    0%, 100% {
        box-shadow: 0 0 15px rgba(255, 107, 0, 0.4);
        transform: scale(1);
    }
    50% {
        box-shadow:
            0 0 25px rgba(255, 107, 0, 0.6),
            0 0 35px rgba(255, 107, 0, 0.4),
            0 0 45px rgba(255, 107, 0, 0.2);
        transform: scale(1.05);
    }
}

@keyframes titleOrangeShimmer {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

@keyframes titleOrangePulse {
    0%, 100% {
        opacity: 0;
        transform: scale(1);
    }
    50% {
        opacity: 0.3;
        transform: scale(1.2);
    }
}

/* === Form Structure === */
.signup-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-field {
    margin-bottom: 0.5rem;
    position: relative;
}

.input-wrapper {
    position: relative;
    width: 100%;
}

.input-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    z-index: 1;
    font-size: 16px;
    height: 16px;
    width: 16px;
}

input {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: #ffffff;
    color: var(--text-color);
    position: relative;
    overflow: hidden;
}

input::placeholder {
    color: var(--text-light);
    font-weight: 400;
}

input:focus {
    border-color: var(--focus-color);
    outline: none;
    box-shadow:
        0 0 0 3px var(--focus-shadow),
        0 0 20px rgba(0, 16, 64, 0.15),
        inset 0 1px 3px rgba(0, 16, 64, 0.1);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    transform: translateY(-1px);
    animation: inputGlow 0.3s ease-out;
}

input:hover:not(:focus) {
    border-color: rgba(0, 16, 64, 0.3);
    box-shadow: 0 2px 8px rgba(0, 16, 64, 0.08);
    transform: translateY(-0.5px);
}

@keyframes inputGlow {
    0% {
        box-shadow: 0 0 0 0px var(--focus-shadow);
    }
    50% {
        box-shadow: 0 0 0 6px var(--focus-shadow);
    }
    100% {
        box-shadow:
            0 0 0 3px var(--focus-shadow),
            0 0 20px rgba(0, 16, 64, 0.15),
            inset 0 1px 3px rgba(0, 16, 64, 0.1);
    }
}

input:focus + .input-icon {
    color: var(--focus-color);
    transform: translateY(-50%) scale(1.1);
    animation: iconPulse 0.3s ease-out;
}

@keyframes iconPulse {
    0% {
        transform: translateY(-50%) scale(1);
    }
    50% {
        transform: translateY(-50%) scale(1.2);
    }
    100% {
        transform: translateY(-50%) scale(1.1);
    }
}

/* Date field special styling */
input[type="date"] {
    color: var(--text-light);
}

input[type="date"]:focus,
input[type="date"]:valid {
    color: var(--text-color);
}

/* CV upload */
.cv-wrapper {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f9f9f9;
    margin-bottom: 0.5rem;
}

.cv-wrapper:hover {
    border-color: var(--primary-color);
    background-color: #f5f5f5;
}

.upload-text {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Terms checkbox */
.terms-field {
    margin: 1rem 0;
}

.terms-checkbox {
    font-size: 0.85rem;
    color: var(--text-color);
}

.terms-link {
    color: var(--orange-color);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
}

.terms-link:hover {
    color: var(--orange-light);
    text-shadow: 0 0 8px rgba(255, 107, 0, 0.4);
    transform: translateY(-1px);
}

.terms-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background: var(--orange-gradient);
    transition: width 0.3s ease;
    border-radius: 1px;
}

.terms-link:hover::after {
    width: 100%;
    box-shadow: 0 0 8px rgba(255, 107, 0, 0.5);
}

/* Password toggle */
.toggle-password {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-light);
    z-index: 2;
}

.toggle-password:hover {
    color: var(--primary-color);
}

/* Error messages */
.error-message {
    color: var(--error-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
}

.error-message mat-icon {
    font-size: 14px;
    margin-right: 4px;
}

/* Sign up button */
.signup-btn {
    width: 100%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 14px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 1rem 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.signup-btn:hover {
    background-color: var(--primary-light);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 22, 96, 0.3);
}

.signup-btn:disabled {
    background-color: var(--text-light);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Sign in link */
.signup-link {
    text-align: center;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: var(--text-light);
}

.signin-link {
    color: var(--orange-color);
    text-decoration: none;
    cursor: pointer;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
    display: inline-block;
}

.signin-link:hover {
    color: var(--orange-light);
    text-shadow: 0 0 8px rgba(255, 107, 0, 0.4);
    transform: translateY(-1px) scale(1.05);
}

.signin-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 50%;
    background: var(--orange-gradient);
    transition: all 0.3s ease;
    border-radius: 1px;
    transform: translateX(-50%);
}

.signin-link:hover::after {
    width: 100%;
    box-shadow: 0 0 8px rgba(255, 107, 0, 0.5);
    animation: orangeLinkGlow 0.6s ease-out;
}

@keyframes orangeLinkGlow {
    0% {
        width: 0;
        box-shadow: 0 0 0px rgba(255, 107, 0, 0);
    }
    50% {
        box-shadow: 0 0 12px rgba(255, 107, 0, 0.7);
    }
    100% {
        width: 100%;
        box-shadow: 0 0 8px rgba(255, 107, 0, 0.5);
    }
}

/* Or divider */
.or-divider {
    text-align: center;
    position: relative;
    margin: 1rem 0;
    color: var(--text-light);
    font-size: 0.85rem;
}

.or-divider::before,
.or-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 40%;
    height: 1px;
    background-color: var(--border-color);
}

.or-divider::before {
    left: 0;
}

.or-divider::after {
    right: 0;
}

.or-divider span {
    background-color: white;
    padding: 0 15px;
    position: relative;
    z-index: 1;
}

/* Social login */
.social-signup {
    text-align: center;
    margin-top: 1rem;
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--white);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-btn:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.social-icon {
    width: 20px;
    height: 20px;
}

/* === Angular Material Overrides === */
::ng-deep .mat-mdc-checkbox {
    .mdc-checkbox {
        margin-right: 8px;
    }

    .mdc-checkbox__background {
        border-color: var(--border-color);
    }

    &.mat-mdc-checkbox-checked {
        .mdc-checkbox__background {
            background-color: var(--accent-color) !important;
            border-color: var(--accent-color) !important;
        }
    }
}

::ng-deep .mat-icon {
    font-family: 'Material Icons' !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* === Responsive Styles === */
@media (max-width: 992px) {
    .signup-grid {
        grid-template-columns: 1fr;
    }

    .logo-section {
        padding: 2rem;
        border-right: none;
        border-bottom: 1px solid rgba(0, 22, 96, 0.08);
    }
}

@media (max-width: 768px) {
    .signup-wrapper {
        padding: 1rem;
    }

    .signup-container {
        border-radius: 12px;
        width: 100%;
    }

    .form-section {
        padding: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .form-title {
        font-size: 1.25rem;
    }

    .logo-section {
        padding: 1.5rem;
    }

    .logo-text {
        font-size: 1.5rem;
    }

    .join-button {
        font-size: 0.75rem;
        padding: 0.6rem 0.8rem;
    }
}

@media (max-width: 576px) {
    .signup-wrapper {
        padding: 0.5rem;
    }

    .form-section {
        padding: 1rem;
    }

    .logo-section {
        padding: 1rem;
    }

    input {
        padding: 10px 10px 10px 36px;
        font-size: 0.85rem;
    }

    .signup-btn {
        padding: 12px;
        font-size: 0.85rem;
    }
}
