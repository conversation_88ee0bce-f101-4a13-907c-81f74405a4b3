import {Component, OnInit} from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidatorFn,
  Validators
} from '@angular/forms';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatIconModule} from '@angular/material/icon';
import {MatInputModule} from '@angular/material/input';
import {MatButtonModule} from '@angular/material/button';
import {MatDividerModule} from '@angular/material/divider';
import {MatMenuModule} from '@angular/material/menu';
import {MatCheckboxModule} from '@angular/material/checkbox';
import {NgIf} from '@angular/common';
import {Router} from '@angular/router';
import {UserService} from '../../../services/user/user.service';
import {PhoneNumberUtil} from 'google-libphonenumber';
import {MatNativeDateModule} from '@angular/material/core';
import {map, Observable} from 'rxjs';
import {SignupStateService} from '../../../services/signup-state.service';

@Component({
  selector: 'app-sign-up',
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatButtonModule,
    MatDividerModule,
    MatMenuModule,
    MatCheckboxModule,
    NgIf,
    MatNativeDateModule
  ],
  templateUrl: './sign-up.component.html',
  standalone: true,
  styleUrls: ['./sign-up.component.scss']
})
export class SignUpComponent implements OnInit {
  form: FormGroup;
  showPassword = false;
  showConfirmPassword = false;
  phoneCodes: { code: string; number: number }[] = [];
  selectedPhoneCode: { code: string; number: number } | null = null;
  favoritesCountries: string[] = ['FR', 'TN'];
  phoneUtil = PhoneNumberUtil.getInstance();
  isSubmitting = false;
  formSubmitted = false;
  phoneMaxLength: number = 15;
  protected selectedFile: File | null = null;
  protected selectedFileName: string | null = null;

  // URL du backend OAuth2 pour la page de connexion
  private signInUrl = 'http://localhost:8080/login'; // Remplacez par l'URL correcte de votre backend OAuth2

  constructor(
    private fb: FormBuilder,
    private router: Router,
    public userService: UserService
  ) {
    this.form = this.fb.group(
      {
        email: ['', [Validators.required, Validators.email], [this.emailExistsValidator()]],
        password: ['', [Validators.required, Validators.minLength(8)]],
        confirmPassword: ['', [Validators.required]],
        firstName: ['', Validators.required],
        lastName: ['', Validators.required],
        birthday: ['', Validators.required],
        phoneNumber: ['', [Validators.required, this.phoneValidator()]],
        cv: [''], // CV is optional
        address: [''], // Address is optional
        agreeToTerms: [false, Validators.requiredTrue]
      },
      {validators: this.passwordMatchValidator}
    );
  }

  ngOnInit() {
    this.initializePhoneCodes();
    this.adjustPhoneMaxLength();
  }

  private initializePhoneCodes() {
    this.phoneUtil.getSupportedRegions().forEach((region) => {
      const item = {code: region, number: this.phoneUtil.getCountryCodeForRegion(region)};
      this.phoneCodes.push(item);
      if (!this.selectedPhoneCode && this.favoritesCountries.includes(region)) {
        this.selectedPhoneCode = item;
      }
    });

    if (!this.selectedPhoneCode) {
      this.selectedPhoneCode = this.phoneCodes.find(code => code.code === 'FR') || this.phoneCodes[0];
    }
  }

  signup() {
    this.formSubmitted = true;

    if (this.form.valid) {
      this.isSubmitting = true;

      const formData = new FormData();
      formData.append('firstName', this.form.get('firstName')?.value || '');
      formData.append('lastName', this.form.get('lastName')?.value || '');
      formData.append('email', this.form.get('email')?.value || '');
      formData.append('password', this.form.get('password')?.value || '');
      formData.append('phoneNumber', `+${this.selectedPhoneCode?.number || '33'}${this.form.get('phoneNumber')?.value || ''}`);
      const birthday = this.form.get('birthday')?.value;
      if (birthday) {
        formData.append('birthday', new Date(birthday).toISOString().split('T')[0]);
      }
      formData.append('address', this.form.get('address')?.value || '');
      formData.append('agreeToTerms', String(this.form.get('agreeToTerms')?.value || false));
      if (this.selectedFile) {
        formData.append('cv', this.selectedFile, this.selectedFile.name);
      }

      // Log FormData for debugging
      for (let [key, value] of formData.entries()) {
        console.log(`FormData ${key}:`, value);
      }

      this.userService.signUp(formData).subscribe({
        next: () => {
          this.isSubmitting = false;
          alert('Inscription réussie ! 🎉 Veuillez vous connecter.');
          window.location.href = this.signInUrl; // Redirection vers le backend OAuth2
        },
        error: (err) => {
          this.isSubmitting = false;
          console.error('Erreur d\'inscription:', err);
          const errorMessage = err.error?.message || err.message || 'Veuillez réessayer.';
          alert('Échec de l\'inscription: ' + errorMessage);
        }
      });
    } else {
      Object.keys(this.form.controls).forEach(key => {
        const control = this.form.get(key);
        if (control?.invalid) {
          console.log(`Control ${key} is invalid:`, control.errors);
        }
      });
      this.form.markAllAsTouched();
      alert('Veuillez corriger les erreurs dans le formulaire ou vérifier tous les champs, y compris les conditions d\'utilisation.');
    }
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  toggleConfirmPasswordVisibility() {
    this.showConfirmPassword = !this.showConfirmPassword;
  }

  hasError(field: string, type: string): boolean {
    const control = this.form.get(field);
    return control ? control.touched && control.hasError(type) : false;
  }

  isFavorite(code: string): boolean {
    return this.favoritesCountries.includes(code);
  }

  goToSignIn() {
    window.location.href = this.signInUrl; // Redirection vers l'URL du backend OAuth2
  }

  private emailExistsValidator(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<{ [key: string]: any } | null> => {
      return this.userService.checkEmail(control.value).pipe(
        map((exists) => (exists ? {emailExists: true} : null))
      );
    };
  }

  private passwordMatchValidator(control: AbstractControl): { [key: string]: any } | null {
    const password = control.get('password')?.value;
    const confirmPassword = control.get('confirmPassword')?.value;
    return password && confirmPassword && password !== confirmPassword ? {passwordsDoNotMatch: true} : null;
  }

  private phoneValidator(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      if (!control.value) {
        return {required: true};
      }
      if (!this.selectedPhoneCode) {
        return {phoneNotValid: true};
      }
      const phone = control.value.replace(/\D/g, '');
      if (phone.length < 7) {
        return {phoneNotValid: true};
      }
      try {
        const fullPhoneNumber = `+${this.selectedPhoneCode.number}${phone}`;
        const phoneNumber = this.phoneUtil.parseAndKeepRawInput(fullPhoneNumber, this.selectedPhoneCode.code);
        return this.phoneUtil.isValidNumber(phoneNumber) ? null : {phoneNotValid: true};
      } catch (e) {
        return {phoneNotValid: true};
      }
    };
  }

  adjustPhoneMaxLength() {
    if (this.selectedPhoneCode?.code) {
      try {
        const phoneNumber = this.phoneUtil.getExampleNumber(this.selectedPhoneCode.code);
        if (phoneNumber) {
          const nationalPhoneNumber = phoneNumber.getNationalNumber();
          if (nationalPhoneNumber) {
            this.phoneMaxLength = nationalPhoneNumber.toString().length;
            const phoneInput = document.querySelector('input[formControlName="phoneNumber"]') as HTMLInputElement;
            if (phoneInput) {
              phoneInput.setAttribute('maxlength', this.phoneMaxLength.toString());
            }
          }
        }
      } catch (e) {
        console.error('Erreur pour obtenir la longueur du numéro : ', e);
        this.phoneMaxLength = 15;
      }
    }
  }

  onEmailInput(event: Event) {

    const input = event.target as HTMLInputElement;
    const email = input.value;
    this.userService.checkEmail(email).subscribe({
      next: (exists) => {
        if (exists) {
          this.form.get('email')?.setErrors({emailExists: true});
        } else {
          this.form.get('email')?.setErrors(null);
        }
      },
      error: (err) => console.error('Error checking email:', err)
    });
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
      this.selectedFileName = this.selectedFile.name;
      this.form.get('cv')?.setValue(this.selectedFileName);
      this.form.get('cv')?.markAsTouched();
    }
  }

  selectPhoneCode(p: { code: string; number: number }) {
    this.selectedPhoneCode = p;
    this.adjustPhoneMaxLength();
    const phoneControl = this.form.get('phoneNumber');
    if (phoneControl?.value) {
      phoneControl.updateValueAndValidity();
    }
  }
}
