/* === Variables === */
:root {
    --primary-color: #001e64; /* Bleu foncé */
    --primary-light: #2a4fd0;
    --primary-dark: #001850;
    --accent-color: #ff6b00; /* Orange */
    --accent-light: #ff8c3a;
    --text-color: #2d3748;
    --text-light: #718096;
    --bg-color: #f0f4ff; /* Fond bleu très clair */
    --white: #ffffff;
    --border-color: #e2e8f0;
    --shadow-color: rgba(0, 30, 100, 0.15);
    --success-color: #4CAF50; /* Vert pour les icônes de check */
    --error-color: #e53e3e;
    --google-color: #DB4437;
    --github-color: #333333;
}

/* === Global Reset and Base Styles === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

body {
    background-color: #f5f7fb;
    color: var(--text-color);
}

/* === Material Icons === */
.material-icons {
    font-family: 'Material Icons' !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--primary-color);
}

/* === Wrapper === */
.signup-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: #f5f7fb;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

/* === Container === */
.signup-container {
    max-width: 900px;
    width: 98%;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    z-index: 1;
    height: auto;
}

/* === Grid Layout === */
.signup-grid {
    display: grid;
    grid-template-columns: 40% 60%;
}

/* === Logo Section === */
.logo-section {
    background-color: var(--bg-color);
    padding: 2.5rem 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    text-align: center;
    position: relative;
    border-right: 1px solid #eaeaea;
}

/* Logo container */
.logo-container {
    margin-bottom: 0.5rem;
}

.logo-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: contain;
    background-color: var(--white);
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.logo-text {
    color: var(--primary-color);
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.logo-underline {
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
    margin: 0 auto 1.5rem;
}

.welcome-text {
    color: var(--text-color);
    font-size: 0.85rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
}

.welcome-text p {
    margin: 0 0.5rem;
}

.welcome-dot {
    width: 4px;
    height: 4px;
    background-color: var(--accent-color);
    border-radius: 50%;
    display: inline-block;
}

/* Benefits list */
.benefits-list {
    width: 100%;
    text-align: left;
    margin-bottom: 1.5rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.check-icon {
    color: var(--success-color);
    margin-right: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.check-icon mat-icon {
    font-size: 16px;
    height: 16px;
    width: 16px;
}

.benefit-item p {
    font-size: 0.8rem;
    color: var(--text-color);
}

.join-button-container {
    margin-top: 1.5rem;
    width: 85%;
}

.join-button {
    background-color: var(--white);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 25px;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.join-button:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* === Form Fields === */
.form-section {
    background-color: var(--white);
    padding: 1.5rem 2rem 1rem;
}

.form-field-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.form-field {
    margin-bottom: 1rem;
    position: relative;
}

.input-wrapper {
    position: relative;
    width: 100%;
}

.input-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    z-index: 1;
    font-size: 16px;
    height: 16px;
    width: 16px;
}

input {
    width: 100%;
    padding: 8px 8px 8px 32px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

input:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* Date field */
.date-wrapper {
    position: relative;
}

.date-label {
    font-size: 0.75rem;
    color: var(--text-color);
    margin-bottom: 0.25rem;
    font-weight: 500;
}

/* CV upload */
.cv-label {
    font-size: 0.75rem;
    color: var(--text-color);
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.cv-wrapper {
    border: 1px solid var(--border-color);
    padding: 0.5rem;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f9f9f9;
}

.cv-wrapper:hover {
    border-color: var(--primary-color);
}

.upload-text {
    color: var(--text-light);
    font-size: 0.75rem;
}

/* Terms checkbox */
.terms-field {
    margin-top: 0.5rem;
    margin-bottom: 1rem;
}

.terms-checkbox {
    font-size: 0.75rem;
}

.terms-link {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
}

.terms-link:hover {
    text-decoration: underline;
}

/* Password toggle */
.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-light);
}

/* Error messages */
.error-message {
    color: var(--error-color);
    font-size: 0.8rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
}

.error-message mat-icon {
    font-size: 16px;
    margin-right: 4px;
}

/* Sign up button */
.signup-btn {
    width: 100%;
    background-color: var(--primary-dark);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
}

.signup-btn:hover {
    background-color: var(--primary-color);
}

.signup-btn:disabled {
    background-color: var(--text-light);
    cursor: not-allowed;
}

/* Sign in link */
.signup-link {
    text-align: center;
    margin-bottom: 0.75rem;
    font-size: 0.75rem;
    color: var(--text-color);
}

.signin-link {
    color: var(--accent-color);
    text-decoration: none;
    cursor: pointer;
    font-weight: 500;
}

.signin-link:hover {
    text-decoration: underline;
}

/* Or divider */
.or-divider {
    text-align: center;
    position: relative;
    margin: 0.75rem 0;
}

.or-divider::before,
.or-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 40%;
    height: 1px;
    background-color: var(--border-color);
}

.or-divider::before {
    left: 0;
}

.or-divider::after {
    right: 0;
}

.or-divider span {
    background-color: white;
    padding: 0 10px;
    font-size: 0.75rem;
    color: var(--text-light);
    position: relative;
    z-index: 1;
}

/* Social login */
.social-signup {
    text-align: center;
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    background-color: var(--white);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.social-btn:hover {
    border-color: var(--primary-color);
}

.social-icon {
    width: 14px;
    height: 14px;
}

/* Title container and elements */
.title-container {
    text-align: center;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.25rem;
    margin-top: 0;
    width: 100%;
}

.form-title {
    color: var(--primary-color);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.title-underline {
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
    margin: 0 auto 1rem;
}

/* === Responsive Styles === */
@media (max-width: 992px) {
    .signup-grid {
        grid-template-columns: 1fr;
    }
    
    .logo-section {
        padding: 2rem;
    }
}

@media (max-width: 576px) {
    .signup-wrapper {
        padding: 0.5rem;
    }

    .signup-container {
        border-radius: 8px;
        width: 100%;
    }

    .logo-section {
        padding: 1.5rem 1rem;
    }

    .form-section {
        padding: 1.5rem 1rem;
    }

    .form-title {
        font-size: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    input {
        padding: 10px 10px 10px 36px;
        font-size: 0.9rem;
    }

    .input-icon {
        left: 10px;
        font-size: 1.1rem;
    }

    .signup-btn {
        padding: 12px;
        font-size: 0.9rem;
    }

    .social-divider::before,
    .social-divider::after {
        width: 40px;
    }

    .error-message {
        font-size: 0.75rem;
    }
    
    .logo-text {
        font-size: 2rem;
    }
    
    .join-button {
        font-size: 0.9rem;
        padding: 0.6rem 1rem;
    }
}
