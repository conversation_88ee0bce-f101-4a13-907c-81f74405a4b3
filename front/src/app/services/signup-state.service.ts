import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SignupStateService {
  private readonly SIGNUP_COMPLETED_KEY = 'signup_completed';
  private readonly PROFILE_MESSAGE_SHOWN_KEY = 'profile_message_shown';

  private signupCompletedSubject = new BehaviorSubject<boolean>(false);
  public signupCompleted$ = this.signupCompletedSubject.asObservable();

  constructor() {
    // Vérifier si une inscription vient d'être complétée
    const signupCompleted = localStorage.getItem(this.SIGNUP_COMPLETED_KEY);
    if (signupCompleted === 'true') {
      this.signupCompletedSubject.next(true);
    }
  }

  /**
   * Marquer qu'une inscription vient d'être complétée
   */
  markSignupCompleted(): void {
    console.log('=== INSCRIPTION TERMINEE ===');
    console.log('SignupStateService - markSignupCompleted called');
    console.log('Marquage de l\'inscription comme terminée...');

    localStorage.setItem(this.SIGNUP_COMPLETED_KEY, 'true');
    this.signupCompletedSubject.next(true);

    console.log('LocalStorage updated - signup_completed: true');
    console.log('L\'utilisateur sera redirigé vers OAuth2 puis le message s\'affichera après connexion');
    console.log('=== FIN MARQUAGE INSCRIPTION ===');
  }

  /**
   * Vérifier si l'utilisateur vient de s'inscrire et n'a pas encore vu le message
   * Le message s'affiche UNE SEULE FOIS après la connexion pour les nouveaux utilisateurs
   */
  shouldShowProfileMessage(): boolean {
    console.log('=== SignupStateService.shouldShowProfileMessage() ===');

    const signupCompletedValue = localStorage.getItem(this.SIGNUP_COMPLETED_KEY);
    const messageShownValue = localStorage.getItem(this.PROFILE_MESSAGE_SHOWN_KEY);

    const signupCompleted = signupCompletedValue === 'true';
    const messageShown = messageShownValue === 'true';

    console.log('SIGNUP_COMPLETED_KEY:', this.SIGNUP_COMPLETED_KEY);
    console.log('PROFILE_MESSAGE_SHOWN_KEY:', this.PROFILE_MESSAGE_SHOWN_KEY);
    console.log('localStorage.getItem(signup_completed):', signupCompletedValue);
    console.log('localStorage.getItem(profile_message_shown):', messageShownValue);
    console.log('signupCompleted (boolean):', signupCompleted);
    console.log('messageShown (boolean):', messageShown);

    const result = signupCompleted && !messageShown;
    console.log('RESULTAT FINAL:', result);
    console.log('=== FIN shouldShowProfileMessage ===');

    // Le message s'affiche UNE SEULE FOIS après inscription + connexion
    return result;
  }

  /**
   * Marquer que le message de profil a été affiché - UNE SEULE FOIS
   */
  markProfileMessageShown(): void {
    console.log('SignupStateService - markProfileMessageShown called');
    // Marquer le message comme affiché pour éviter qu'il se répète
    localStorage.setItem(this.PROFILE_MESSAGE_SHOWN_KEY, 'true');
    // Nettoyer le flag d'inscription après avoir montré le message
    localStorage.removeItem(this.SIGNUP_COMPLETED_KEY);
    this.signupCompletedSubject.next(false);
    console.log('Message marked as shown - will not appear again for this user');
  }

  /**
   * Nettoyer tous les flags (utile lors de la déconnexion)
   */
  clearSignupState(): void {
    console.log('SignupStateService - clearSignupState called');
    localStorage.removeItem(this.SIGNUP_COMPLETED_KEY);
    localStorage.removeItem(this.PROFILE_MESSAGE_SHOWN_KEY);
    this.signupCompletedSubject.next(false);
    console.log('All signup state cleared');
  }

  /**
   * Vérifier si l'utilisateur est en cours de processus d'inscription
   */
  isSignupInProgress(): boolean {
    return this.signupCompletedSubject.value;
  }

  /**
   * Méthode de test pour forcer l'affichage du message
   */
  forceShowMessage(): void {
    console.log('ForceShowMessage - Setting up test scenario');
    localStorage.setItem(this.SIGNUP_COMPLETED_KEY, 'true');
    this.signupCompletedSubject.next(true);
    console.log('Test scenario ready - reload page to see message');
  }

  /**
   * Méthode de test pour nettoyer l'état
   */
  resetForTesting(): void {
    console.log('ResetForTesting - Clearing all state');
    this.clearSignupState();
  }
}
