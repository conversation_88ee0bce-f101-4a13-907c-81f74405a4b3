import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SignupStateService {
  private readonly SIGNUP_COMPLETED_KEY = 'signup_completed';
  private readonly PROFILE_MESSAGE_SHOWN_KEY = 'profile_message_shown';

  private signupCompletedSubject = new BehaviorSubject<boolean>(false);
  public signupCompleted$ = this.signupCompletedSubject.asObservable();

  constructor() {
    // Vérifier si une inscription vient d'être complétée
    const signupCompleted = localStorage.getItem(this.SIGNUP_COMPLETED_KEY);
    if (signupCompleted === 'true') {
      this.signupCompletedSubject.next(true);
    }
  }

  /**
   * Marquer qu'une inscription vient d'être complétée
   */
  markSignupCompleted(): void {
    console.log('SignupStateService - markSignupCompleted called');
    localStorage.setItem(this.SIGNUP_COMPLETED_KEY, 'true');
    this.signupCompletedSubject.next(true);
    console.log('LocalStorage updated - signup_completed: true');
  }

  /**
   * Vérifier si l'utilisateur vient de s'inscrire (toujours afficher pour chaque nouvelle inscription)
   */
  shouldShowProfileMessage(): boolean {
    const signupCompleted = localStorage.getItem(this.SIGNUP_COMPLETED_KEY) === 'true';

    console.log('SignupStateService - shouldShowProfileMessage check:');
    console.log('  signupCompleted:', signupCompleted);
    console.log('  result:', signupCompleted);

    // Le message s'affiche toujours après une inscription, peu importe si déjà vu
    return signupCompleted;
  }

  /**
   * Marquer que le message de profil a été affiché et nettoyer l'état d'inscription
   */
  markProfileMessageShown(): void {
    console.log('SignupStateService - markProfileMessageShown called');
    // Nettoyer le flag d'inscription après avoir montré le message
    // Cela permet au message de s'afficher à chaque nouvelle inscription
    localStorage.removeItem(this.SIGNUP_COMPLETED_KEY);
    this.signupCompletedSubject.next(false);
    console.log('Signup state cleared - ready for next signup');
  }

  /**
   * Nettoyer tous les flags (utile lors de la déconnexion)
   */
  clearSignupState(): void {
    console.log('SignupStateService - clearSignupState called');
    localStorage.removeItem(this.SIGNUP_COMPLETED_KEY);
    // On ne supprime plus le flag PROFILE_MESSAGE_SHOWN_KEY car on ne l'utilise plus
    this.signupCompletedSubject.next(false);
    console.log('All signup state cleared');
  }

  /**
   * Vérifier si l'utilisateur est en cours de processus d'inscription
   */
  isSignupInProgress(): boolean {
    return this.signupCompletedSubject.value;
  }

  /**
   * Méthode de test pour forcer l'affichage du message
   */
  forceShowMessage(): void {
    console.log('ForceShowMessage - Setting up test scenario');
    localStorage.setItem(this.SIGNUP_COMPLETED_KEY, 'true');
    this.signupCompletedSubject.next(true);
    console.log('Test scenario ready - reload page to see message');
  }

  /**
   * Méthode de test pour nettoyer l'état
   */
  resetForTesting(): void {
    console.log('ResetForTesting - Clearing all state');
    this.clearSignupState();
  }
}
